# lme-fleet-server.container
[Unit]
Description=Fleet Container Service
Requires=lme-elasticsearch.service
After=lme-elasticsearch.service lme-kibana.service
PartOf=lme.service
ConditionPathExists=/opt/lme/FLEET_SETUP_FINISHED

[Service]
Restart=always
TimeoutStartSec=5400
Environment=ANSIBLE_VAULT_PASSWORD_FILE=/etc/lme/pass.sh

[Install]
WantedBy=default.target lme.service

[Container]
ContainerName=lme-fleet-server
Environment=FLEET_SERVER_POLICY_ID=fleet-server-policy KIBANA_HOST=https://lme-kibana:5601 FLEET_URL=https://lme-fleet-server:8220 FLEET_SERVER_ELASTICSEARCH_HOST=https://lme-elasticsearch:9200 FLEET_CA=/certs/ca/ca.crt FLEET_SERVER_CERT=/certs/fleet-server/fleet-server.crt FLEET_SERVER_CERT_KEY=/certs/fleet-server/fleet-server.key FLEET_SERVER_ELASTICSEARCH_CA=/certs/ca/ca.crt KIBANA_FLEET_CA=/certs/ca/ca.crt NODE_EXTRA_CA_CERTS=/etc/ssl/certs/ca-certificates.crt ELASTICSEARCH_HOST=https://lme-elasticsearch:9200 ELASTICSEARCH_HOSTS=https://lme-elasticsearch:9200 ES_HOSTS=https://lme-elasticsearch:9200 ELASTIC_AGENT_ELASTICSEARCH_HOST=https://lme-elasticsearch:9200
EnvironmentFile=/opt/lme/lme-environment.env
Secret=elastic,type=env,target=KIBANA_FLEET_PASSWORD
Image=localhost/elastic-agent:LME_LATEST
Network=lme
HostName=lme-fleet-server
PodmanArgs=--network-alias lme-fleet-server --requires 'lme-elasticsearch,lme-kibana'
PublishPort=8220:8220
Volume=lme_certs:/certs:ro
Volume=lme_fleet_data:/usr/share/elastic-agent
UserNS=auto:uidmapping=0:171632:3048,gidmapping=0:171632:3048

#TODO: fix this, need to check if its ready before polling API
#HealthCmd=CMD-SHELL curl -s --cacert /certs/ca/ca.crt https://localhost:8220/api/status | grep '"status":"HEALTHY"'
#Notify=healthy
