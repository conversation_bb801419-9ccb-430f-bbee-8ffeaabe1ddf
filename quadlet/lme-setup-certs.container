# lme-elasticsearch-security-setup.container
[Unit] 
Requires=lme-network.service lme-esdata01-volume.service lme-kibanadata-volume.service
After=lme.service lme-network.service lme-esdata01-volume.service lme-kibanadata-volume.service
PartOf=lme.service

[Service]
Type=oneshot
RemainAfterExit=yes
Environment=ANSIBLE_VAULT_PASSWORD_FILE=/etc/lme/pass.sh

[Install]
WantedBy=default.target lme.service

[Container]
ContainerName=lme-setup-certs
EnvironmentFile=/opt/lme/lme-environment.env 
Secret=elastic,type=env,target=ELASTIC_PASSWORD
Secret=kibana_system,type=env,target=KIBANA_PASSWORD
Exec=/bin/bash /usr/share/elasticsearch/config/setup/init-setup.sh
Image=localhost/elasticsearch:LME_LATEST
Network=lme
PodmanArgs=--network-alias lme-setup --health-interval=2s 
Volume=lme_certs:/usr/share/elasticsearch/config/certs
Volume=lme_esdata01:/usr/share/elasticsearch/data
Volume=/opt/lme/config/setup:/usr/share/elasticsearch/config/setup
User=0
#UserNS=auto:uidmapping=165536:165536:3048,gidmapping=165536:165536:3048
UserNS=auto
