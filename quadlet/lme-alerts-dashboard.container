# lme-alerts-dashboard.container
[Unit]
Description=LME Alerts Dashboard Container Service
Requires=lme-elasticsearch.service lme-kibana.service
After=lme-elasticsearch.service lme-kibana.service
PartOf=lme.service

[Service]
Restart=always
Environment=ANSIBLE_VAULT_PASSWORD_FILE=/etc/lme/pass.sh

[Install]
WantedBy=default.target lme.service

[Container]
ContainerName=lme-alerts-dashboard
Environment=ELASTICSEARCH_URL=https://lme-elasticsearch:9200 ELASTICSEARCH_USERNAME=elastic POLL_INTERVAL=60000
EnvironmentFile=/opt/lme/lme-environment.env
Secret=elastic,type=env,target=ELASTICSEARCH_PASSWORD
Image=localhost/alerts-dashboard:LME_LATEST
Network=lme
PodmanArgs=--network-alias lme-alerts-dashboard --health-interval=30s
PublishPort=8080:80
Volume=lme_certs:/etc/nginx/certs:ro
UserNS=auto
HealthCmd=CMD-SHELL curl -f http://localhost:80/ || exit 1
Notify=healthy
