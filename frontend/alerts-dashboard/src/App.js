import React, { useState, useEffect } from 'react';
import { Box, Container, Typography, CircularProgress, AppBar, Toolbar, Tabs, Tab } from '@mui/material';
import AlertsList from './components/AlertsList';
import AlertsFilter from './components/AlertsFilter';
import AlertsStats from './components/AlertsStats';
import AlertsCorrelation from './components/AlertsCorrelation';
import { fetchAlerts } from './services/elasticsearchService';

function App() {
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [filters, setFilters] = useState({
    severity: [],
    timeRange: '24h',
    searchTerm: '',
  });
  const [stats, setStats] = useState({
    total: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
  });

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Poll for alerts at regular intervals
  useEffect(() => {
    const loadAlerts = async () => {
      try {
        setLoading(true);
        const { alerts: fetchedAlerts, stats: fetchedStats } = await fetchAlerts(filters);
        setAlerts(fetchedAlerts);
        setStats(fetchedStats);
        setError(null);
      } catch (err) {
        console.error('Error fetching alerts:', err);
        setError('Failed to fetch alerts. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Load alerts immediately
    loadAlerts();

    // Set up polling interval
    const pollInterval = 60000; // 60 seconds
    const intervalId = setInterval(loadAlerts, pollInterval);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    setFilters({ ...filters, ...newFilters });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <AppBar position="static" color="primary" elevation={0}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            LME Alerts Dashboard
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 4, mb: 4, flexGrow: 1 }}>
        <AlertsStats stats={stats} />

        <Box sx={{ mt: 3, mb: 3 }}>
          <AlertsFilter filters={filters} onFilterChange={handleFilterChange} />
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="alert views">
            <Tab label="Alerts List" />
            <Tab label="Correlation Network" />
          </Tabs>
        </Box>

        {loading && alerts.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ mt: 4, p: 3, bgcolor: 'error.light', borderRadius: 2 }}>
            <Typography color="error" variant="body1">
              {error}
            </Typography>
          </Box>
        ) : (
          <Box>
            {activeTab === 0 && <AlertsList alerts={alerts} loading={loading} />}
            {activeTab === 1 && <AlertsCorrelation alerts={alerts} />}
          </Box>
        )}
      </Container>

      <Box component="footer" sx={{ py: 3, px: 2, mt: 'auto', backgroundColor: 'background.paper' }}>
        <Container maxWidth="sm">
          <Typography variant="body2" color="text.secondary" align="center">
            LME Alerts Dashboard © {new Date().getFullYear()}
          </Typography>
        </Container>
      </Box>
    </Box>
  );
}

export default App;
