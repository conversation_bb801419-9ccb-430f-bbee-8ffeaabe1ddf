import axios from 'axios';

// Mock data for demonstration purposes
const MOCK_ALERTS = [
  {
    id: '1',
    timestamp: new Date(Date.now() - 5 * 60000).toISOString(), // 5 minutes ago
    rule: 'Suspicious Login Activity',
    severity: 'critical',
    message: 'Multiple failed login attempts detected from unusual location',
    source: 'workstation-104',
    new: true
  },
  {
    id: '2',
    timestamp: new Date(Date.now() - 15 * 60000).toISOString(), // 15 minutes ago
    rule: 'Malware Detection',
    severity: 'high',
    message: 'Potential malware detected: Trojan.GenericKD.45678123',
    source: 'server-db-01',
    new: false
  },
  {
    id: '3',
    timestamp: new Date(Date.now() - 30 * 60000).toISOString(), // 30 minutes ago
    rule: 'Unusual Network Traffic',
    severity: 'medium',
    message: 'Unusual outbound traffic to IP ************ on port 8080',
    source: 'workstation-156',
    new: false
  },
  {
    id: '4',
    timestamp: new Date(Date.now() - 45 * 60000).toISOString(), // 45 minutes ago
    rule: 'File Integrity Monitoring',
    severity: 'medium',
    message: 'Critical system file modified: /etc/passwd',
    source: 'server-web-02',
    new: false
  },
  {
    id: '5',
    timestamp: new Date(Date.now() - 60 * 60000).toISOString(), // 1 hour ago
    rule: 'Privilege Escalation',
    severity: 'high',
    message: 'User attempted to gain root privileges',
    source: 'workstation-112',
    new: false
  },
  {
    id: '6',
    timestamp: new Date(Date.now() - 90 * 60000).toISOString(), // 1.5 hours ago
    rule: 'Brute Force Attack',
    severity: 'critical',
    message: 'Brute force attack detected against SSH service',
    source: 'server-web-01',
    new: false
  },
  {
    id: '7',
    timestamp: new Date(Date.now() - 120 * 60000).toISOString(), // 2 hours ago
    rule: 'Suspicious Process Execution',
    severity: 'high',
    message: 'Suspicious process spawned from unusual parent process',
    source: 'workstation-132',
    new: false
  },
  {
    id: '8',
    timestamp: new Date(Date.now() - 180 * 60000).toISOString(), // 3 hours ago
    rule: 'Configuration Change',
    severity: 'low',
    message: 'Firewall configuration changed',
    source: 'server-fw-01',
    new: false
  },
  {
    id: '9',
    timestamp: new Date(Date.now() - 240 * 60000).toISOString(), // 4 hours ago
    rule: 'Unusual User Behavior',
    severity: 'medium',
    message: 'User accessed sensitive files outside normal working hours',
    source: 'workstation-118',
    new: false
  },
  {
    id: '10',
    timestamp: new Date(Date.now() - 300 * 60000).toISOString(), // 5 hours ago
    rule: 'Data Exfiltration Attempt',
    severity: 'critical',
    message: 'Large data transfer to external IP detected',
    source: 'server-db-02',
    new: false
  },
  {
    id: '11',
    timestamp: new Date(Date.now() - 360 * 60000).toISOString(), // 6 hours ago
    rule: 'Ransomware Activity',
    severity: 'critical',
    message: 'Multiple files encrypted in short timeframe',
    source: 'workstation-125',
    new: false
  },
  {
    id: '12',
    timestamp: new Date(Date.now() - 420 * 60000).toISOString(), // 7 hours ago
    rule: 'Unauthorized Access',
    severity: 'high',
    message: 'User accessed resources without proper permissions',
    source: 'server-app-01',
    new: false
  }
];

// Helper to convert time range to milliseconds
const timeRangeToMs = (timeRange) => {
  const value = parseInt(timeRange.slice(0, -1), 10);
  const unit = timeRange.slice(-1);

  switch (unit) {
    case 'h':
      return value * 60 * 60 * 1000;
    case 'd':
      return value * 24 * 60 * 60 * 1000;
    default:
      return 24 * 60 * 60 * 1000; // Default to 24 hours
  }
};

// Fetch alerts (using mock data for demo)
export const fetchAlerts = async (filters) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Calculate time range
    const now = new Date();
    const timeRangeMs = timeRangeToMs(filters.timeRange);
    const fromDate = new Date(now.getTime() - timeRangeMs);

    // Filter alerts based on time range
    let filteredAlerts = MOCK_ALERTS.filter(alert => {
      const alertDate = new Date(alert.timestamp);
      return alertDate >= fromDate && alertDate <= now;
    });

    // Apply severity filter if selected
    if (filters.severity && filters.severity.length > 0) {
      filteredAlerts = filteredAlerts.filter(alert =>
        filters.severity.map(s => s.toLowerCase()).includes(alert.severity.toLowerCase())
      );
    }

    // Apply search term filter if provided
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filteredAlerts = filteredAlerts.filter(alert =>
        alert.rule.toLowerCase().includes(searchLower) ||
        alert.message.toLowerCase().includes(searchLower) ||
        alert.source.toLowerCase().includes(searchLower)
      );
    }

    // Calculate stats
    const stats = {
      total: filteredAlerts.length,
      critical: filteredAlerts.filter(a => a.severity === 'critical').length,
      high: filteredAlerts.filter(a => a.severity === 'high').length,
      medium: filteredAlerts.filter(a => a.severity === 'medium').length,
      low: filteredAlerts.filter(a => a.severity === 'low').length,
    };

    return { alerts: filteredAlerts, stats };
  } catch (error) {
    console.error('Error fetching alerts:', error);
    throw new Error('Failed to fetch alerts');
  }
};
