import React from 'react';
import { Grid, Paper, Typography, Box } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import NotificationsIcon from '@mui/icons-material/Notifications';

const StatCard = ({ title, value, icon, color }) => {
  return (
    <Paper
      sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        height: 120,
        borderTop: 4,
        borderColor: color,
        borderRadius: '12px',
        boxShadow: '0 4px 12px 0 rgba(0,0,0,0.05)',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Box sx={{ color, mr: 1 }}>{icon}</Box>
        <Typography component="h2" variant="h6" color="text.secondary">
          {title}
        </Typography>
      </Box>
      <Typography component="p" variant="h3">
        {value}
      </Typography>
    </Paper>
  );
};

const AlertsStats = ({ stats }) => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Total Alerts"
          value={stats.total}
          icon={<NotificationsIcon />}
          color="primary.main"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Critical"
          value={stats.critical}
          icon={<ErrorIcon />}
          color="error.main"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="High"
          value={stats.high}
          icon={<ErrorIcon />}
          color="error.light"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Medium"
          value={stats.medium}
          icon={<WarningIcon />}
          color="warning.main"
        />
      </Grid>
    </Grid>
  );
};

export default AlertsStats;
