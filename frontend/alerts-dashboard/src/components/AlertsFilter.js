import React from 'react';
import { 
  Box, 
  Paper, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField, 
  Grid,
  Chip,
  OutlinedInput,
  Checkbox,
  ListItemText
} from '@mui/material';

const SEVERITY_OPTIONS = ['Critical', 'High', 'Medium', 'Low'];
const TIME_RANGE_OPTIONS = [
  { value: '1h', label: 'Last hour' },
  { value: '6h', label: 'Last 6 hours' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
];

const AlertsFilter = ({ filters, onFilterChange }) => {
  const handleSeverityChange = (event) => {
    onFilterChange({ severity: event.target.value });
  };

  const handleTimeRangeChange = (event) => {
    onFilterChange({ timeRange: event.target.value });
  };

  const handleSearchChange = (event) => {
    onFilterChange({ searchTerm: event.target.value });
  };

  return (
    <Paper sx={{ p: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="severity-select-label">Severity</InputLabel>
            <Select
              labelId="severity-select-label"
              id="severity-select"
              multiple
              value={filters.severity}
              onChange={handleSeverityChange}
              input={<OutlinedInput id="select-multiple-chip" label="Severity" />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </Box>
              )}
            >
              {SEVERITY_OPTIONS.map((severity) => (
                <MenuItem key={severity} value={severity}>
                  <Checkbox checked={filters.severity.indexOf(severity) > -1} />
                  <ListItemText primary={severity} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={4} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="time-range-select-label">Time Range</InputLabel>
            <Select
              labelId="time-range-select-label"
              id="time-range-select"
              value={filters.timeRange}
              label="Time Range"
              onChange={handleTimeRangeChange}
            >
              {TIME_RANGE_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={4} md={6}>
          <TextField
            fullWidth
            id="search-field"
            label="Search alerts"
            variant="outlined"
            size="small"
            value={filters.searchTerm}
            onChange={handleSearchChange}
            placeholder="Search by rule name, message, or source"
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AlertsFilter;
