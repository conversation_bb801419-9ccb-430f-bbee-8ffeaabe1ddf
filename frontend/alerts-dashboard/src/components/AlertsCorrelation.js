import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Card,
  CardContent,
  Chip,
  Grid
} from '@mui/material';
import * as d3 from 'd3';

const AlertsCorrelation = ({ alerts }) => {
  const svgRef = useRef(null);
  const [correlationType, setCorrelationType] = useState('all');
  const [loading, setLoading] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleAlertClick = (alert) => {
    setSelectedAlert(alert);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Generate correlation data from alerts
  const generateCorrelationData = (alerts, type) => {
    // Create nodes from alerts
    const nodes = alerts.map(alert => ({
      id: alert.id,
      name: alert.rule,
      severity: alert.severity,
      source: alert.source,
      timestamp: new Date(alert.timestamp),
      message: alert.message
    }));

    // Create links between alerts based on correlation type
    let links = [];

    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const sourceNode = nodes[i];
        const targetNode = nodes[j];

        // Time-based correlation (alerts within 10 minutes of each other)
        const timeDiff = Math.abs(sourceNode.timestamp - targetNode.timestamp);
        const isTimeCorrelated = timeDiff < 10 * 60 * 1000; // 10 minutes

        // Source-based correlation (same source)
        const isSourceCorrelated = sourceNode.source === targetNode.source;

        // Severity-based correlation (same severity)
        const isSeverityCorrelated = sourceNode.severity === targetNode.severity;

        // Text-based correlation (similar messages)
        const isTextCorrelated =
          sourceNode.message.toLowerCase().includes(targetNode.message.toLowerCase().substring(0, 10)) ||
          targetNode.message.toLowerCase().includes(sourceNode.message.toLowerCase().substring(0, 10));

        // Add link based on correlation type
        if (type === 'all' ||
            (type === 'time' && isTimeCorrelated) ||
            (type === 'source' && isSourceCorrelated) ||
            (type === 'severity' && isSeverityCorrelated) ||
            (type === 'text' && isTextCorrelated)) {

          // Calculate correlation strength
          let strength = 0;
          if (isTimeCorrelated) strength += 0.25;
          if (isSourceCorrelated) strength += 0.25;
          if (isSeverityCorrelated) strength += 0.25;
          if (isTextCorrelated) strength += 0.25;

          // Only add links with some correlation
          if (strength > 0) {
            links.push({
              source: sourceNode.id,
              target: targetNode.id,
              strength: strength
            });
          }
        }
      }
    }

    return { nodes, links };
  };

  const handleCorrelationTypeChange = (event) => {
    setCorrelationType(event.target.value);
  };

  // Create and update the visualization
  useEffect(() => {
    if (!alerts || alerts.length === 0 || !svgRef.current) return;

    setLoading(true);

    // Clear previous visualization
    d3.select(svgRef.current).selectAll("*").remove();

    // Generate correlation data
    const data = generateCorrelationData(alerts, correlationType);

    // Set up SVG
    const width = svgRef.current.clientWidth;
    const height = 500;

    const svg = d3.select(svgRef.current)
      .attr("width", width)
      .attr("height", height);

    // Add zoom functionality
    const zoom = d3.zoom()
      .scaleExtent([0.5, 5])
      .on("zoom", (event) => {
        g.attr("transform", event.transform);
      });

    svg.call(zoom);

    // Create a group for the graph
    const g = svg.append("g");

    // Define severity colors
    const severityColor = {
      critical: "#d32f2f",
      high: "#f44336",
      medium: "#ff9800",
      low: "#2196f3"
    };

    // Create a force simulation
    const simulation = d3.forceSimulation(data.nodes)
      .force("link", d3.forceLink(data.links).id(d => d.id).distance(100))
      .force("charge", d3.forceManyBody().strength(-200))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(30));

    // Create links
    const link = g.append("g")
      .selectAll("line")
      .data(data.links)
      .enter().append("line")
      .attr("stroke-width", d => d.strength * 5)
      .attr("stroke", "#999")
      .attr("stroke-opacity", 0.6);

    // Create node groups
    const nodeGroup = g.append("g")
      .selectAll("g")
      .data(data.nodes)
      .enter().append("g")
      .call(d3.drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended))
      .on("click", (event, d) => {
        // Find the original alert
        const originalAlert = alerts.find(alert => alert.id === d.id);
        if (originalAlert) {
          handleAlertClick(originalAlert);
        }
      });

    // Add circles to node groups
    nodeGroup.append("circle")
      .attr("r", d => {
        // Size based on severity
        switch(d.severity) {
          case 'critical': return 15;
          case 'high': return 12;
          case 'medium': return 10;
          case 'low': return 8;
          default: return 8;
        }
      })
      .attr("fill", d => severityColor[d.severity] || "#999")
      .style("cursor", "pointer");

    // Add labels to nodes
    nodeGroup.append("text")
      .attr("dx", d => {
        // Position based on severity
        switch(d.severity) {
          case 'critical': return 18;
          case 'high': return 15;
          case 'medium': return 13;
          case 'low': return 11;
          default: return 11;
        }
      })
      .attr("dy", ".35em")
      .text(d => d.name.length > 20 ? d.name.substring(0, 20) + '...' : d.name)
      .style("font-size", "10px")
      .style("pointer-events", "none");

    // Add tooltips
    nodeGroup.append("title")
      .text(d => `${d.name}\nSeverity: ${d.severity}\nSource: ${d.source}\nTime: ${d.timestamp.toLocaleString()}\nClick for details`);

    // Update positions on each tick
    simulation.on("tick", () => {
      link
        .attr("x1", d => d.source.x)
        .attr("y1", d => d.source.y)
        .attr("x2", d => d.target.x)
        .attr("y2", d => d.target.y);

      nodeGroup
        .attr("transform", d => `translate(${d.x}, ${d.y})`);
    });

    // Drag functions
    function dragstarted(event, d) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event, d) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }

    setLoading(false);

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [alerts, correlationType]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get severity color for display
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Paper sx={{ p: 2, mt: 3, position: 'relative', minHeight: 550 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h2">
          Alert Correlation Network
        </Typography>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel id="correlation-type-label">Correlation Type</InputLabel>
          <Select
            labelId="correlation-type-label"
            id="correlation-type"
            value={correlationType}
            label="Correlation Type"
            onChange={handleCorrelationTypeChange}
          >
            <MenuItem value="all">All Correlations</MenuItem>
            <MenuItem value="time">Time-based</MenuItem>
            <MenuItem value="source">Source-based</MenuItem>
            <MenuItem value="severity">Severity-based</MenuItem>
            <MenuItem value="text">Content-based</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, bgcolor: 'rgba(255,255,255,0.7)', zIndex: 1 }}>
          <CircularProgress />
        </Box>
      )}

      {alerts.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <Typography variant="body1" color="text.secondary">
            No alerts to correlate. Try adjusting your filters.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ width: '100%', height: 500, overflow: 'hidden' }}>
          <svg ref={svgRef} style={{ width: '100%', height: '100%' }}></svg>
        </Box>
      )}

      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          This visualization shows relationships between alerts based on common attributes.
          Larger nodes represent higher severity alerts. Thicker lines indicate stronger correlations.
          Drag nodes to explore relationships, use mouse wheel to zoom in/out, and click on nodes to see details.
        </Typography>
      </Box>

      {/* Alert Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedAlert && (
          <>
            <DialogTitle>
              Alert Details
              <Chip
                label={selectedAlert.severity}
                color={getSeverityColor(selectedAlert.severity)}
                size="small"
                sx={{ ml: 2 }}
              />
            </DialogTitle>
            <DialogContent>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        {selectedAlert.rule}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        Source
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedAlert.source}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        Time
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {formatDate(selectedAlert.timestamp)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        Message
                      </Typography>
                      <Typography variant="body1" paragraph>
                        {selectedAlert.message}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        Related Alerts
                      </Typography>
                      <Typography variant="body1">
                        {alerts.filter(alert =>
                          alert.id !== selectedAlert.id &&
                          (alert.source === selectedAlert.source ||
                           alert.severity === selectedAlert.severity ||
                           Math.abs(new Date(alert.timestamp) - new Date(selectedAlert.timestamp)) < 10 * 60 * 1000)
                        ).length} related alerts found
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default AlertsCorrelation;
