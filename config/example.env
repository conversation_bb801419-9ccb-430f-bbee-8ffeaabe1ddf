######################################
## MAKE SURE TO SET THE BELOW VALUE: #
######################################
#IP of your host machine
IPVAR=127.0.0.1

# LME Version
LME_VERSION=2.1.0

# ElasticSearch settings
########################

#TODO: this will be needed for scaling, not needed right now
# the names of the OS nodes
#ES_NODE1=es01
# uncomment to create a cluster (more nodes can be added also)
# !!! do not forget to also adjust the docker-compose.yml file !!!
# ES_NODE2=es02

# Local Kibana URL
LOCAL_KBN_URL=https://127.0.0.1:5601
# Local ES URL
LOCAL_ES_URL=https://127.0.0.1:9200

# Elastic settings
#################

# Version of Elastic products
STACK_VERSION=8.18.0
# Testing pre-releases? Use the SNAPSHOT option below:
# STACK_VERSION=8.11.0-SNAPSHOT
#
# Set the cluster name
CLUSTER_NAME=LME

#User info:
#Username used by elastic service for admin, currently this is static
ELASTIC_USERNAME=elastic
# Password for the 'elastic' user (at least 6 characters)
# ansible-vault: elastic
#ELASTIC_PASSWORD=password1

#Username used by kibana, currently this is static
ELASTICSEARCH_USERNAME=kibana_system
# Password for the 'kibana_system' user (at least 6 characters)
# ansible-vault: kibana_system
#KIBANA_PASSWORD=password1

#Fleet:
KIBANA_FLEET_USERNAME=elastic
# ansible-vault: elastic
#KIBANA_FLEET_PASSWORD=password1

# Fleet Server enrollment - set to 1 for initial enrollment, 0 to prevent re-enrollment on restart
FLEET_ENROLL=1

#Wazuh:
# ansible-vault: wazuh
#WAZUH_PASSWORD=MyP@ssw0rd1#
INDEXER_USERNAME=elastic
# ansible-vault: elastic
#INDEXER_PASSWORD=password1
API_USERNAME=wazuh-wui 
# ansible-vault: wazuh_api
#API_PASSWORD=MyP@ssw0rd1#

# Set to "basic" or "trial" to automatically start the 30-day trial
LICENSE=basic

#TODO: support changing these, right now they're static
# Port to expose Elasticsearch HTTP API to the host
ES_PORT=9200
#ES_PORT=127.0.0.1:9200
# Port to expose Kibana to the host
KIBANA_PORT=5601
# Port to expose Fleet to the host
FLEET_PORT=8220

# Increase or decrease based on the available host memory (in bytes)
MEM_LIMIT=**********


# Detection Settings: 
#################
#TODO: integrate fleet setup into postinstall ansible script
# Bulk Enable Detection Rules by OS - change to "1" if you want to enable

LinuxDR=0
WindowsDR=0
MacOSDR=0

# Proxy Settings: 
# LEAVE BLANK IF NO PROXY!
#################

# Standard certificate location for ubuntu
#PROXY_CA_LOCATION=/etc/ssl/certs/ca-certificates.crt
# Proxy Server URL
#PROXY_URL=
# IPs and host names you want the proxy to ignore. Typically want all private IP's and Docker network hostnames / IP's ignored
# Example config:
# 127.0.0.1,localhost,10.,172.16.,172.17.,192.168.,*.local,.local,169.254/16,lme-elasticsearch,lme-kibana,lme-fleet-server,lme-wazuh-manager
#PROXY_IGNORE=
#set these as well:
#HTTP_PROXY=
#HTTPS_PROXY=
#NO_PROXY=
