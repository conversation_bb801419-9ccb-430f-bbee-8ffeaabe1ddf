# SMTP Email notification configuration
# Important: This file should NOT have a .yml or .yaml extension

alert:
  - email
smtp_auth_file: "/opt/elastalert/misc/smtp_auth.yml"
email: "<EMAIL>"
from_addr: "<EMAIL>"
smtp_host: "smtp.example.com"
smtp_port: 587
smtp_ssl: false
alert_text_type: alert_text_only
alert_text: |
  Security Alert
  Severity: {0}
  Rule: {1}
  Agent: {2}
  Action: {3}
  Timestamp: {4}
alert_text_args:
  - kibana.alert.severity
  - kibana.alert.rule.name
  - agent.name
  - event.action
  - kibana.alert.rule.execution.timestamp