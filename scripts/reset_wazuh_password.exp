#!/usr/bin/expect -f

# Usage: ./reset_wazuh_password.exp <username> <password>

# Get command line arguments
set username [lindex $argv 0]
set password [lindex $argv 1]

# Initialize counter for updates
set updates 0

# Start the rbac_control command
spawn podman exec -it lme-wazuh-manager /var/ossec/bin/rbac_control change-password

# Wait for any password prompt and send password
expect {
    "New password for" {
        send "$password\r"
        exp_continue
    }
    "UPDATED" {
        incr updates
        if {$updates == 2} {
            exit 0
        }
        exp_continue
    }
    timeout {
        puts "Timeout waiting for prompt"
        exit 1
    }
}

# Wait for completion
expect eof

exit 0 