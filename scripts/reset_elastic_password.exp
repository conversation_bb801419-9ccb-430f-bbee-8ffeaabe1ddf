#!/usr/bin/expect -f
# Set a longer timeout
set timeout 10

# Disable output
log_user 0

# Get username and password from arguments
set username [lindex $argv 0]
set password [lindex $argv 1]

spawn podman exec -it lme-elasticsearch sh
expect "\\\$ "
send "bin/elasticsearch-reset-password -u $username -i\r"
expect "Please confirm"
send "y\r"
expect "Enter password"
send "$password\r"
expect "Re-enter password"
send "$password\r"
expect "successfully reset"
expect "\\\$ "
send "exit\r"
expect eof 