#!/bin/bash

# Script to build the alerts dashboard container using Docker

set -e

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

echo "Building alerts dashboard container with Docker..."

# Build the container
docker build -t localhost/alerts-dashboard:LME_LATEST -f "$PROJECT_ROOT/docker/alerts-dashboard/Dockerfile" "$PROJECT_ROOT"

echo "Alerts dashboard container built successfully!"
