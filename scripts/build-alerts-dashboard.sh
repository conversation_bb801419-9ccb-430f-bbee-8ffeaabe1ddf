#!/bin/bash

# <PERSON>ript to build the alerts dashboard container

set -e

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

echo "Building alerts dashboard container..."

# Build the container
podman build -t localhost/alerts-dashboard:LME_LATEST -f "$PROJECT_ROOT/docker/alerts-dashboard/Dockerfile" "$PROJECT_ROOT"

echo "Alerts dashboard container built successfully!"
