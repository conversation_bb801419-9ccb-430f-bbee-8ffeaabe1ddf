*.pdf
*.docx
.DS_Store
/.idea/
/.vscode/
**/.env
/Chapter 4 Files/*.dumped.ndjson
/Chapter 4 Files/exported/

#created files should be ignored:
Chapter 3 Files/certs/
Chapter 3 Files/docker-compose-stack-live.yml
Chapter 3 Files/logstash.edited.conf
Chapter 3 Files/logstash_custom.conf
LME/
files_for_windows.zip
lme.conf
**/venv/
/testing/tests/.env
**/.pytest_cache/
**/__pycache__/
/testing/*.password.txt
/testing/configure/azure_scripts/config.ps1
/testing/configure.zip
/testing/*.output.log
/testing/tests/report.html
testing/tests/assets/style.css
.history/
**/get-docker.sh
*.vim
**.password.txt
**.ip.txt
**.swp
*.vim*
**/quadlet/output
**/lme-environment.env
**/env.sh
testing/v2/installers/envirnment.sh
docker/**/environment.sh
**exporter.txt
testing/upgrade_testing/
.aider*
.aider/
.cache/
.venv/
*.env
