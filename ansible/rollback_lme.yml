---
- name: Rollback LME to Previous Version
  hosts: localhost
  connection: local
  become: yes
  vars:
    # Common variables
    install_user: "{{ ansible_user_id }}"
    lme_install_dir: "/opt/lme"
    
  tasks:
    - name: Determine podman command location
      shell: |
        # Check multiple locations for podman
        if command -v podman >/dev/null 2>&1; then
          echo "podman"
        elif [ -x "/nix/var/nix/profiles/default/bin/podman" ]; then
          echo "/nix/var/nix/profiles/default/bin/podman"
        elif [ -x "/usr/local/bin/podman" ]; then
          echo "/usr/local/bin/podman"
        else
          echo "ERROR: podman command not found in any expected location" >&2
          echo "Checked locations:" >&2
          echo "- Standard PATH: $(which podman 2>/dev/null || echo 'not found')" >&2
          echo "- Nix location: /nix/var/nix/profiles/default/bin/podman" >&2
          echo "- Symlink location: /usr/local/bin/podman" >&2
          exit 1
        fi
      args:
        executable: /bin/bash
      environment:
        PATH: "{{ ansible_env.PATH }}:/nix/var/nix/profiles/default/bin"
      register: podman_cmd_result
      
    - name: Check if podman symlink exists
      stat:
        path: "/usr/local/bin/podman"
      register: podman_symlink_stat
      
    - name: Check if nix podman exists
      stat:
        path: "/nix/var/nix/profiles/default/bin/podman"
      register: nix_podman_stat
      
    - name: Create podman symlink if needed and nix podman exists
      file:
        src: "/nix/var/nix/profiles/default/bin/podman"
        dest: "/usr/local/bin/podman"
        state: link
        force: yes
      when: 
        - not podman_symlink_stat.stat.exists
        - nix_podman_stat.stat.exists
      ignore_errors: yes
      
    - name: Set podman command variable
      set_fact:
        podman_cmd: "{{ podman_cmd_result.stdout }}"
        
    - name: Display podman command location
      debug:
        msg: "Using podman from: {{ podman_cmd }}"

    - name: Get Podman graphroot location
      shell: |
        # Get the full path to the storage directory
        GRAPHROOT=$({{ podman_cmd }} info --format "{{ '{{' }}.Store.GraphRoot{{ '}}' }}")
        if [ -z "$GRAPHROOT" ]; then
          echo "/var/lib/containers/storage"  # Default fallback
        else
          echo "$GRAPHROOT"
        fi
      args:
        executable: /bin/bash
      environment:
        PATH: "{{ ansible_env.PATH }}:/nix/var/nix/profiles/default/bin"
      register: podman_graphroot
      
    - name: Set backup directory
      set_fact:
        backup_base_dir: "{{ podman_graphroot.stdout }}"
      
    - name: Get list of available backups
      find:
        paths: "{{ backup_base_dir }}/backups"
        patterns: "20*"
        file_type: directory
      register: available_backups
      
    - name: Debug backup paths
      debug:
        msg: "Found {{ available_backups.files | length }} backups in {{ backup_base_dir }}/backups"
        
    - name: Create backup paths list
      set_fact:
        backup_paths: "{{ available_backups.files | sort(attribute='path') | map(attribute='path') | list }}"

    - name: Check if any backups are available
      fail:
        msg: |
          No LME backups found in {{ backup_base_dir }}/backups

          To create a backup before rollback, you can run:
          ansible-playbook ansible/backup_lme.yml

          Or check if the backup directory path is correct.
          Current backup directory: {{ backup_base_dir }}/backups
      when: backup_paths | length == 0

    - name: Get stack version for each backup
      shell: |
        if [ -f "{{ item }}/lme/lme-environment.env" ]; then
          STACK_VERSION=$(grep "^STACK_VERSION=" "{{ item }}/lme/lme-environment.env" | cut -d'=' -f2 2>/dev/null || echo "Unknown")
          LME_VERSION=$(grep "^LME_VERSION=" "{{ item }}/lme/lme-environment.env" | cut -d'=' -f2 2>/dev/null)
          if [ -z "$LME_VERSION" ]; then
            LME_VERSION="2.0.x"
          fi
          echo "Stack: $STACK_VERSION, LME: $LME_VERSION"
        else
          echo "Unknown"
        fi
      args:
        executable: /bin/bash
      loop: "{{ backup_paths }}"
      register: backup_versions

    - name: Create backup info list
      set_fact:
        backup_info: "{{ backup_info | default([]) + [{'path': item.item, 'version': item.stdout, 'basename': item.item | basename}] }}"
      loop: "{{ backup_versions.results }}"

    - name: Create backup display list
      set_fact:
        backup_display_list: "{{ backup_display_list | default([]) + [item.basename + ' (Version: ' + item.version + ')'] }}"
      loop: "{{ backup_info }}"
      when: backup_info is defined and backup_info | length > 0

    - name: Show backup list with versions
      debug:
        msg: "{{ dict(range(1, backup_display_list|length + 1) | zip(backup_display_list)) }}"
      when: backup_display_list is defined and backup_display_list | length > 0
      
    - name: Prompt for backup selection
      pause:
        prompt: |
          Please enter the number of the backup to restore from (1-{{ backup_info | length }}):
          Note: Backups are sorted by date, with the newest backup at the bottom.
      register: backup_selection
      when: backup_info is defined and backup_info | length > 0
      
    - name: Validate backup selection
      fail:
        msg: "Invalid selection. Please enter a number between 1 and {{ backup_info | length }}"
      when: 
        - backup_info is defined and backup_info | length > 0
        - backup_selection.user_input | int < 1 or backup_selection.user_input | int > backup_info | length
      
    - name: Set selected backup path
      set_fact:
        selected_backup_dir: "{{ backup_info[backup_selection.user_input | int - 1].path }}"
      when: backup_info is defined and backup_info | length > 0
      
    - name: Get valid backup choice
      pause:
        prompt: |
          {% if backup_choice_loop is defined and backup_choice_loop.user_input is defined %}
          Invalid input '{{ backup_choice_loop.user_input }}'. Please enter y/yes or n/no.
          {% endif %}
          Do you want to create a safety backup of the current installation before rollback? (y/yes/n/no)
          This is highly recommended in case the rollback fails.
      register: backup_choice_loop
      until: backup_choice_loop.user_input | lower in ['y', 'yes', 'n', 'no']
      retries: 999
      delay: 0
      
    - name: Set backup choice
      set_fact:
        backup_choice: "{{ backup_choice_loop.user_input | lower }}"
      
    - name: Display backup in progress message
      debug:
        msg: "Creating safety backup of current installation before rollback. This may take a few minutes..."
      when: backup_choice in ['y', 'yes']
        
    - name: Run backup
      include_role:
        name: backup_lme
      vars:
        skip_prompts: true  # Skip prompts during automated backup
        skip_service_restart: true  # Leave services stopped for rollback operations
      register: backup_role_result
      when: backup_choice in ['y', 'yes']
      
    - name: Set backup success variable for compatibility when backup is done
      set_fact:
        current_lme_backup: "{{ backup_role_result }}"
      when: backup_choice in ['y', 'yes']
      
    - name: Stop LME service when backup is skipped
      systemd:
        name: lme
        state: stopped
      when: backup_choice in ['n', 'no']
      
    - name: Wait for containers to stop when backup is skipped
      shell: |
        {{ podman_cmd }} ps -a --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true
      args:
        executable: /bin/bash
      register: running_containers_no_backup
      when: backup_choice in ['n', 'no']
      until: running_containers_no_backup.stdout_lines | length == 0
      retries: 12
      delay: 5
      ignore_errors: yes
      when: backup_choice in ['n', 'no']
      
    - name: Set backup success variable for compatibility when backup is skipped
      set_fact:
        current_lme_backup: { "skipped": true, "msg": "Backup skipped by user choice" }
      when: backup_choice in ['n', 'no']
      
    - name: Verify backup exists
      stat:
        path: "{{ selected_backup_dir }}"
      register: backup_stat
      
    - name: Fail if no backup found
      fail:
        msg: "No backup found at {{ selected_backup_dir }}"
      when: not backup_stat.stat.exists
      
    - name: Check for volume backups
      stat:
        path: "{{ selected_backup_dir }}/volumes"
      register: volume_backup_dir

    - name: Verify backup can be read
      stat:
        path: "{{ selected_backup_dir }}/lme"
        get_checksum: no
      register: lme_backup_check
      
    - name: Validate backup directory
      fail:
        msg: "The LME backup directory doesn't exist or is not accessible: {{ selected_backup_dir }}/lme"
      when: not lme_backup_check.stat.exists or not lme_backup_check.stat.isdir
      
    - name: Remove the current installation if backup and validation succeeded
      shell: |
        if [ -d "{{ lme_install_dir }}" ]; then
          rm -rf "{{ lme_install_dir }}"
          echo "Removed current installation at {{ lme_install_dir }}"
        fi
      args:
        executable: /bin/bash
      when: >
        (
          (backup_choice in ['y', 'yes'] and current_lme_backup is success) or
          (backup_choice in ['n', 'no'] and current_lme_backup.skipped is defined)
        ) and lme_backup_check.stat.exists
      
    - name: Create rollback status file
      copy:
        dest: "/tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status"
        content: |
          Rollback started: {{ ansible_date_time.iso8601 }}
          {% if backup_choice in ['y', 'yes'] %}
          Safety backup created at: {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}
          {% else %}
          Safety backup: SKIPPED by user choice
          {% endif %}
          Rollback source: {{ selected_backup_dir }}
          Status: IN PROGRESS
      
    - name: Restore LME installation, vault files, and systemd files
      shell: |
        # Restore LME installation
        mkdir -p {{ lme_install_dir }}
        cp -a {{ selected_backup_dir }}/lme/. {{ lme_install_dir }}/
        LME_RESTORE_STATUS=$?
        
        # Restore vault files if they exist in backup
        if [ -d "{{ selected_backup_dir }}/etc_lme" ]; then
          mkdir -p /etc/lme
          cp -af {{ selected_backup_dir }}/etc_lme/. /etc/lme/
          VAULT_RESTORE_STATUS=$?
          
          # Ensure proper permissions on restored vault files
          chmod 700 /etc/lme
          chmod 700 /etc/lme/vault
          chmod 600 /etc/lme/vault/* 2>/dev/null || true
          chmod 700 /etc/lme/pass.sh 2>/dev/null || true
          chown -R root:root /etc/lme
          
          echo "Vault files restored from backup with proper permissions"
        else
          echo "No vault files found in backup (older backup format)"
          VAULT_RESTORE_STATUS=0
        fi
        
        # Restore systemd container files if they exist in backup
        if [ -d "{{ selected_backup_dir }}/etc_containers_systemd" ]; then
          mkdir -p /etc/containers/systemd
          cp -af {{ selected_backup_dir }}/etc_containers_systemd/. /etc/containers/systemd/
          SYSTEMD_RESTORE_STATUS=$?
          
          # Ensure proper permissions on restored systemd files
          chmod 644 /etc/containers/systemd/*.container 2>/dev/null || true
          chmod 644 /etc/containers/systemd/*.volume 2>/dev/null || true
          chmod 644 /etc/containers/systemd/*.network 2>/dev/null || true
          chown root:root /etc/containers/systemd/* 2>/dev/null || true
          
          echo "Systemd container files restored from backup with proper permissions"
        else
          echo "No systemd container files found in backup (older backup format)"
          SYSTEMD_RESTORE_STATUS=0
        fi
        
        if [ $LME_RESTORE_STATUS -eq 0 ] && [ $VAULT_RESTORE_STATUS -eq 0 ] && [ $SYSTEMD_RESTORE_STATUS -eq 0 ]; then
          echo "LME installation, vault files, and systemd files restored successfully"
          exit 0
        else
          echo "Failed to restore LME installation, vault files, or systemd files (LME: $LME_RESTORE_STATUS, Vault: $VAULT_RESTORE_STATUS, Systemd: $SYSTEMD_RESTORE_STATUS)"
          exit 1
        fi
      args:
        executable: /bin/bash
      when: backup_stat.stat.exists
      register: restore_result
      
    - name: Reload systemd daemon after restoring systemd files
      systemd:
        daemon_reload: yes
      when: restore_result is success
      
    - name: Restore Podman secrets from backup vault files
      shell: |
        export PATH=$PATH:/nix/var/nix/profiles/default/bin
        # Use the backup password file for decryption
        export ANSIBLE_VAULT_PASSWORD_FILE="{{ selected_backup_dir }}/etc_lme/pass.sh"
        
        # Remove existing secrets first
        for secret_id in $(podman secret ls --format "{{ '{{' }}.ID{{ '}}' }}"); do
          podman secret rm "$secret_id" 2>/dev/null || true
        done
        
        # Check if secret mapping file exists in backup
        MAPPING_FILE="{{ selected_backup_dir }}/secret_mapping.txt"
        BACKUP_VAULT_DIR="{{ selected_backup_dir }}/etc_lme/vault"
        
        if [ -f "$MAPPING_FILE" ] && [ -d "$BACKUP_VAULT_DIR" ]; then
          echo "Found secret mapping file and backup vault directory, restoring secrets..."
          
          # Read mapping file and recreate secrets from backup vault files
          while IFS='=' read -r secret_name vault_file_id; do
            # Skip comments and empty lines
            if [[ "$secret_name" =~ ^#.*$ ]] || [[ -z "$secret_name" ]]; then
              continue
            fi
            
            # Check if vault file exists in backup
            BACKUP_VAULT_FILE="$BACKUP_VAULT_DIR/$vault_file_id"
            if [ -f "$BACKUP_VAULT_FILE" ]; then
              echo "Restoring secret: $secret_name from backup vault file: $vault_file_id"
              # Extract secret from backup vault file and create new secret
              SECRET_VALUE=$(ansible-vault view "$BACKUP_VAULT_FILE")
              if [ $? -eq 0 ] && [ -n "$SECRET_VALUE" ]; then
                echo "$SECRET_VALUE" | podman secret create --driver shell --replace "$secret_name" -
                if [ $? -eq 0 ]; then
                  echo "Successfully restored secret: $secret_name"
                else
                  echo "Failed to create secret: $secret_name"
                fi
              else
                echo "Failed to decrypt backup vault file for secret: $secret_name"
              fi
            else
              echo "Warning: Backup vault file not found for secret $secret_name: $BACKUP_VAULT_FILE"
            fi
          done < "$MAPPING_FILE"
          
          echo "Secret restoration from backup completed"
        else
          echo "No secret mapping file or backup vault directory found, attempting fallback method..."
          
          # Fallback: Create secrets in standard order from backup vault files (for older backups)
          if [ -d "$BACKUP_VAULT_DIR" ]; then
            vault_files=($(ls "$BACKUP_VAULT_DIR" | grep -v "^$" | sort))
            secret_names=("elastic" "kibana_system" "wazuh_api" "wazuh")
            
            for i in "${!secret_names[@]}"; do
              if [ -f "$BACKUP_VAULT_DIR/${vault_files[$i]}" ]; then
                echo "Creating secret ${secret_names[$i]} from backup vault file ${vault_files[$i]} (fallback method)"
                SECRET_VALUE=$(ansible-vault view "$BACKUP_VAULT_DIR/${vault_files[$i]}")
                if [ $? -eq 0 ] && [ -n "$SECRET_VALUE" ]; then
                  echo "$SECRET_VALUE" | podman secret create --driver shell --replace "${secret_names[$i]}" -
                else
                  echo "Failed to decrypt backup vault file: ${vault_files[$i]}"
                fi
              fi
            done
          else
            echo "Error: No backup vault directory found at $BACKUP_VAULT_DIR"
          fi
          
          echo "Fallback secret creation from backup completed"
        fi
        
        # Verify secrets were created
        echo "Current secrets:"
        podman secret ls
      args:
        executable: /bin/bash
      when: restore_result is success
      register: secrets_restore_result
      ignore_errors: yes
      
    - name: Extract version from backup status file
      shell: |
        if [ -f "{{ selected_backup_dir }}/backup_status.txt" ]; then
          grep "^LME version:" "{{ selected_backup_dir }}/backup_status.txt" | cut -d':' -f2 | tr -d ' ' || echo "2.0.2"
        else
          echo "2.0.2"
        fi
      register: backup_version
      
    - name: Extract versions from selected backup environment file
      shell: |
        if [ -f "{{ selected_backup_dir }}/lme/lme-environment.env" ]; then
          STACK_VERSION=$(grep "^STACK_VERSION=" "{{ selected_backup_dir }}/lme/lme-environment.env" | cut -d'=' -f2 2>/dev/null || echo "Unknown")
          LME_VERSION=$(grep "^LME_VERSION=" "{{ selected_backup_dir }}/lme/lme-environment.env" | cut -d'=' -f2 2>/dev/null || echo "{{ backup_version.stdout }}")
          echo "STACK:$STACK_VERSION,LME:$LME_VERSION"
        else
          echo "STACK:Unknown,LME:{{ backup_version.stdout }}"
        fi
      register: restored_versions

    - name: Set version variables for display
      set_fact:
        restored_stack_version: "{{ restored_versions.stdout.split(',')[0].split(':')[1] }}"
        restored_lme_version: "{{ restored_versions.stdout.split(',')[1].split(':')[1] }}"

    - name: Check for containers.txt file
      stat:
        path: "{{ lme_install_dir }}/config/containers.txt"
      register: containers_file
      
    - name: Read containers list from file
      slurp:
        path: "{{ lme_install_dir }}/config/containers.txt"
      register: containers_content
      when: containers_file.stat.exists
      
    - name: Set containers list variable
      set_fact:
        container_list: "{{ (containers_content.content | b64decode).strip().split('\n') | select('match', '^[^#].*') | list }}"
      when: containers_file.stat.exists and containers_content is defined
      
    - name: Pull containers
      shell: |
        {{ podman_cmd }} pull {{ item }}
      args:
        executable: /bin/bash
      loop: "{{ container_list | default([]) }}"
      register: pull_result
      retries: 3
      delay: 5
      until: pull_result is not failed
      ignore_errors: yes
      when: containers_file.stat.exists and container_list is defined and container_list | length > 0
      
    - name: Display container pull errors
      debug:
        msg: "Failed to pull container: {{ item.item }}"
      loop: "{{ pull_result.results | default([]) }}"
      when: pull_result is defined and pull_result.results is defined and item is failed
      loop_control:
        label: "{{ item.item }}"
      
    - name: Tag containers
      shell: |
        {{ podman_cmd }} image tag {{ item }} {{ item.split('/')[-1].split(':')[0] }}:LME_LATEST
      args:
        executable: /bin/bash
      loop: "{{ container_list | default([]) }}"
      register: tag_result
      retries: 3
      delay: 5
      until: tag_result is not failed
      ignore_errors: yes
      when: containers_file.stat.exists and container_list is defined and container_list | length > 0
      
    - name: Display container tag errors
      debug:
        msg: "Failed to tag container: {{ item.item }}"
      loop: "{{ tag_result.results | default([]) }}"
      when: tag_result is defined and tag_result.results is defined and item is failed
      loop_control:
        label: "{{ item.item }}"
      
    - name: Ensure LME service is stopped before volume operations
      systemd:
        name: lme
        state: stopped
      register: service_stop_for_volumes
      
    - name: Wait for all containers to stop before volume operations
      shell: |
        {{ podman_cmd }} ps -a --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true
      args:
        executable: /bin/bash
      register: running_containers_before_volumes
      until: running_containers_before_volumes.stdout_lines | length == 0
      retries: 24
      delay: 5
      ignore_errors: yes
      
    - name: Display any containers still running
      debug:
        msg: "Warning: The following containers are still running: {{ running_containers_before_volumes.stdout_lines }}"
      when: running_containers_before_volumes.stdout_lines | length > 0
    
    - name: Restore volume backups if available
      block:
        # Note: Services are now guaranteed to be stopped before volume operations
        - name: Get list of volume backups
          find:
            paths: "{{ selected_backup_dir }}/volumes"
            file_type: directory
          register: volume_dirs
          
        - name: Display volumes to restore
          debug:
            msg: "Found {{ volume_dirs.files | length }} volume backups to restore"
            
        - name: Get volume names
          set_fact:
            volume_names: "{{ volume_dirs.files | map(attribute='path') | map('basename') | list }}"
          
        - name: Validate volume names exist
          fail:
            msg: "No volume backups found in {{ selected_backup_dir }}/volumes"
          when: volume_names | length == 0

        - name: Remove existing volumes
          shell: |
            if {{ podman_cmd }} volume exists "{{ item }}"; then
              {{ podman_cmd }} volume rm "{{ item }}"
            fi
          args:
            executable: /bin/bash
          loop: "{{ volume_names }}"
          register: volume_remove_result

        - name: Create new volumes
          shell: |
            {{ podman_cmd }} volume create "{{ item }}"
          args:
            executable: /bin/bash
          loop: "{{ volume_names }}"
          register: volume_create_result

        - name: Get volume mount points
          shell: |
            {{ podman_cmd }} volume inspect "{{ item }}" --format "{{ '{{' }}.Mountpoint{{ '}}' }}"
          args:
            executable: /bin/bash
          loop: "{{ volume_names }}"
          register: volume_paths

        - name: Restore volume data
          shell: |
            VOLUME_PATH=$({{ podman_cmd }} volume inspect "{{ item.item }}" --format "{{ '{{' }}.Mountpoint{{ '}}' }}")
            BACKUP_DIR="{{ selected_backup_dir }}/volumes/{{ item.item }}/data"
            
            if [ -d "$BACKUP_DIR" ]; then
              # Copy the backup data to the volume
              cp -a "$BACKUP_DIR/." "$VOLUME_PATH/"
              if [ $? -eq 0 ]; then
                echo "Restored {{ item.item }} to ${VOLUME_PATH}"
                exit 0
              else
                echo "Failed to restore {{ item.item }}"
                exit 1
              fi
            else
              echo "No backup found for {{ item.item }}"
              exit 1
            fi
          args:
            executable: /bin/bash
          loop: "{{ volume_paths.results }}"
          register: volume_restore_result

        - name: Set volume list for verification
          set_fact:
            volume_list: "{{ volume_names | join(' ') }}"

        - name: Check if expected empty volumes file exists in backup
          stat:
            path: "{{ selected_backup_dir }}/expected_empty_volumes.txt"
          register: expected_volumes_file

        - name: Read expected empty volumes from backup file
          shell: cat "{{ selected_backup_dir }}/expected_empty_volumes.txt"
          register: expected_volumes_content
          when: expected_volumes_file.stat.exists

        - name: Set expected empty volumes from backup file
          set_fact:
            expected_empty_volumes: "{{ expected_volumes_content.stdout_lines }}"
          when: expected_volumes_file.stat.exists

        - name: Set fallback expected empty volumes for older backups
          set_fact:
            expected_empty_volumes:
              - lme_backups
              - lme_wazuh_var_multigroups
              - lme_elastalert2_logs
          when: not expected_volumes_file.stat.exists

        - name: Verify volume restoration
          shell: |
            VOLUME_PATH=$({{ podman_cmd }} volume inspect "{{ item }}" --format "{{ '{{' }}.Mountpoint{{ '}}' }}")
            if [ -d "$VOLUME_PATH" ]; then
              # Check if this is an expected empty volume first - always treat as successful
              if [[ " {{ expected_empty_volumes | join(' ') }} " =~ " {{ item }} " ]]; then
                if [ "$(ls -A "$VOLUME_PATH")" ]; then
                  echo "{\"msg\": \"Volume {{ item }} is expected to be empty but contains data - treating as successful at $VOLUME_PATH\", \"status\": \"success\"}"
                else
                  echo "{\"msg\": \"Volume {{ item }} is empty as expected at $VOLUME_PATH\", \"status\": \"success\"}"
                fi
                exit 0
              elif [ "$(ls -A "$VOLUME_PATH")" ]; then
                echo "{\"msg\": \"Volume {{ item }} exists and is not empty at $VOLUME_PATH\", \"status\": \"success\"}"
                exit 0
              else
                echo "{\"msg\": \"Volume {{ item }} exists but is empty at $VOLUME_PATH\", \"status\": \"error\"}"
                exit 1
              fi
            else
              echo "{\"msg\": \"Volume {{ item }} not found at $VOLUME_PATH\", \"status\": \"error\"}"
              exit 1
            fi
          args:
            executable: /bin/bash
          loop: "{{ volume_names }}"
          register: volume_verify_result
          ignore_errors: yes

        - name: Set initial failed volumes
          set_fact:
            failed_volumes: "{{ volume_verify_result.results | selectattr('rc', 'ne', 0) | map(attribute='item') | list }}"

        - name: Filter out expected empty volumes
          set_fact:
            failed_volumes: "{{ failed_volumes | difference(expected_empty_volumes) }}"

        - name: Display volume verification results
          debug:
            msg: "{{ item.stdout_lines }}"
          loop: "{{ volume_verify_result.results }}"
          when: item.stdout_lines | length > 0

        - name: Fail if volume verification failed
          fail:
            msg: |
              Volume verification failed. The following volumes may not have been restored properly:
              {% for volume in failed_volumes %}- {{ volume }}
              {% endfor %}
              
              Please check the backup files in {{ selected_backup_dir }}/volumes/ for these volumes.
              
              Note: The following volumes are always treated as successful regardless of content:
              {% for volume in expected_empty_volumes %}- {{ volume }}
              {% endfor %}
          when: failed_volumes | length > 0

        - name: Update status to post-volume-restore
          copy:
            dest: "/tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status"
            content: |
              Rollback started: {{ ansible_date_time.iso8601 }}
              {% if backup_choice in ['y', 'yes'] %}
              Safety backup created at: {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}
              {% else %}
              Safety backup: SKIPPED by user choice
              {% endif %}
              Rollback source: {{ selected_backup_dir }}
              Status: IN PROGRESS
              Stage: POST_VOLUME_RESTORE
              Volumes restored: {{ volume_names | join(', ') }}
              Failed volumes: {{ failed_volumes | join(', ') if failed_volumes | length > 0 else 'None' }}
              Volumes always treated as successful: {{ expected_empty_volumes | join(', ') }}
        
      when: volume_backup_dir.stat is defined and volume_backup_dir.stat.exists
      
    - name: Start LME service
      systemd:
        name: lme
        state: started
      register: service_start_result
      
    - name: Wait for containers to start
      shell: |
        {{ podman_cmd }} ps --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true
      args:
        executable: /bin/bash
      register: started_containers
      until: started_containers.stdout_lines | length > 0
      retries: 24
      delay: 10
      ignore_errors: yes
      
    - name: Wait for services to be ready
      pause:
        seconds: 60
      
    - name: Check container status
      shell: |
        echo "=== Running Containers ==="
        {{ podman_cmd }} ps --format "{{ '{{' }}.Names{{ '}}' }}: {{ '{{' }}.Status{{ '}}' }}" | grep -E "lme" || echo "No LME containers running"
        echo -e "\n=== Container Logs ==="
        for container in $({{ podman_cmd }} ps -a --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme"); do
          echo "--- $container logs ---"
          {{ podman_cmd }} logs --tail 5 $container
        done
      args:
        executable: /bin/bash
      register: container_status
      changed_when: false

    - name: Display container status
      debug:
        msg: "{{ container_status.stdout_lines | join('\n') }}"

    - name: Verify services are running
      shell: |
        # Ensure podman is available - check multiple locations
        if command -v podman >/dev/null 2>&1; then
          PODMAN_CMD="podman"
        elif [ -x "/nix/var/nix/profiles/default/bin/podman" ]; then
          PODMAN_CMD="/nix/var/nix/profiles/default/bin/podman"
        elif [ -x "/usr/local/bin/podman" ]; then
          PODMAN_CMD="/usr/local/bin/podman"
        else
          echo "ERROR: podman command not found in any expected location"
          echo "Checked locations:"
          echo "- Standard PATH: $(which podman 2>/dev/null || echo 'not found')"
          echo "- Nix location: /nix/var/nix/profiles/default/bin/podman"
          echo "- Symlink location: /usr/local/bin/podman"
          exit 1
        fi
        
        echo "Using podman from: $PODMAN_CMD"
        
        LME_CONTAINERS=$($PODMAN_CMD ps --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true)
        CONTAINER_COUNT=$(echo "$LME_CONTAINERS" | grep -v '^$' | wc -l)
        
        echo "=== Container Status Check ==="
        echo "Running LME containers:"
        echo "$LME_CONTAINERS"
        echo "Container count: $CONTAINER_COUNT"
        
        # List all LME containers (running and stopped)
        echo -e "\n=== All LME Containers ==="
        $PODMAN_CMD ps -a --format "{{ '{{' }}.Names{{ '}}' }}: {{ '{{' }}.Status{{ '}}' }}" | grep -E "lme" || echo "No LME containers found"
        
        # Check if we have at least 4 containers running (allow for some flexibility)
        if [ "$CONTAINER_COUNT" -ge 4 ]; then
          echo -e "\nSufficient LME services are running ($CONTAINER_COUNT/5)"
          exit 0
        else
          echo -e "\nInsufficient LME services running ($CONTAINER_COUNT/5)"
          exit 1
        fi
      args:
        executable: /bin/bash
      environment:
        PATH: "{{ ansible_env.PATH }}:/nix/var/nix/profiles/default/bin"
      register: containers_status
      until: containers_status.rc == 0
      retries: 20
      delay: 20
      changed_when: false

    - name: Fail if not all services are running
      fail:
        msg: |
          Insufficient LME services are running after rollback.
          
          Expected: 5 containers
          Running: {{ containers_status.stdout_lines | select('match', '^lme') | list | length if containers_status.stdout_lines is defined else 'Unknown' }}
          
          Container status details:
          {{ containers_status.stdout_lines | join('\n') if containers_status.stdout_lines is defined else 'No status available' }}
          
          This may be a temporary issue. You can:
          1. Wait a few more minutes for containers to fully start
          2. Check container logs: podman logs [container_name]
          3. Restart the LME service: systemctl restart lme
          4. Check the detailed status file: /tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status
      when: containers_status.rc != 0
      
    - name: Collect rollback results
      set_fact:
        install_status: "{{ 'Success' if backup_stat.stat.exists else 'Failed' }}"
        volumes_status: "{{ 'Success' if volume_restore_result is success else 'Failed' }}"
        containers_running_status: "{{ 'Success' if containers_status.rc == 0 else 'Partial' }}"
      
    - name: Update rollback status file
      copy:
        dest: "/tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status"
        content: |
          Rollback completed: {{ ansible_date_time.iso8601 }}
          
          Restored versions:
          - LME Version: {{ restored_lme_version }}
          - Stack Version: {{ restored_stack_version }}
          
          {% if backup_choice in ['y', 'yes'] %}
          Safety backup created at: {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}
          {% else %}
          Safety backup: SKIPPED by user choice
          {% endif %}
          Rollback source: {{ selected_backup_dir }}
          
          Restore status:
          - LME installation: {{ install_status }}
          - Vault files: {{ 'Success' if restore_result is success else 'Failed' }}
          - Systemd files: {{ 'Success' if restore_result is success else 'Failed' }}
          - Secrets restoration: {{ 'Success' if secrets_restore_result is success else 'Failed' }}
          - Containers pull/tag: {{ 'Success' if (pull_result is defined and pull_result is success) and (tag_result is defined and tag_result is success) else ('Skipped' if not containers_file.stat.exists else 'Partial - Some errors occurred') }}
          - Volumes: {{ volumes_status }}
          - Containers running: {{ containers_running_status }}
          
          Overall status: {{ 'SUCCESS' if install_status == 'Success' and containers_running_status == 'Success' and service_start_result is success else 'PARTIAL - See details above' }}
          
          Recovery instructions:
          {% if backup_choice in ['n', 'no'] %}
          Note: No safety backup was created during this rollback.
          If the rollback fails, you will need to restore from an existing backup or reinstall LME.
          {% endif %}
          {% if install_status != 'Success' %}
          If the rollback failed during installation:
          1. Stop the LME service: systemctl stop lme
          2. Remove the failed installation: rm -rf {{ lme_install_dir }}
          {% if backup_choice in ['y', 'yes'] %}
          3. Restore the safety backup: cp -a {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}/lme/. {{ lme_install_dir }}/
          {% else %}
          3. Restore from an existing backup or reinstall LME
          {% endif %}
          4. Start the service: systemctl start lme
          {% elif tag_result is failed %}
          If the rollback failed during container tagging:
          1. Stop the LME service: systemctl stop lme
          2. Remove the failed installation: rm -rf {{ lme_install_dir }}
          {% if backup_choice in ['y', 'yes'] %}
          3. Restore the safety backup: cp -a {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}/lme/. {{ lme_install_dir }}/
          {% else %}
          3. Restore from an existing backup or reinstall LME
          {% endif %}
          4. Start the service: systemctl start lme
          {% elif volumes_status != 'Success' %}
          If the rollback failed during volume restoration:
          1. Stop the LME service: systemctl stop lme
          2. For each volume:
             a. Remove the current volume: podman volume rm [volume_name]
             b. Create a new volume: podman volume create [volume_name]
          {% if backup_choice in ['y', 'yes'] %}
             c. Restore from safety backup: cp -a {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}/volumes/[volume_name]/data/. [volume_mountpoint]/
          {% else %}
             c. Restore from an existing backup or recreate the volume data
          {% endif %}
          3. Start the service: systemctl start lme
          {% elif containers_running_status != 'Success' %}
          If the rollback failed during container startup:
          1. Check container logs: podman logs [container_name]
          2. Verify container images: podman images
          3. Try restarting the service: systemctl restart lme
          {% endif %}

          {% if backup_choice in ['y', 'yes'] %}
          Cleanup instructions (after confirming successful rollback):
          1. Remove the safety backup created during rollback: rm -rf {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}
          {% endif %}

    - name: Rollback complete message
      debug:
        msg: |
          LME has been rolled back to the previous version.
          
          Restored versions:
          - LME Version: {{ restored_lme_version }}
          - Stack Version: {{ restored_stack_version }}
          
          {% if backup_choice in ['y', 'yes'] %}
          Safety backup created at: {{ backup_base_dir }}/backups/{{ ansible_date_time.iso8601_basic_short }}
          {% else %}
          Safety backup: SKIPPED by user choice
          {% endif %}
          Restored from: {{ selected_backup_dir }}
          
          Restore status:
          - LME installation: {{ install_status }}
          - Vault files: {{ 'Success' if restore_result is success else 'Failed' }}
          - Systemd files: {{ 'Success' if restore_result is success else 'Failed' }}
          - Secrets restoration: {{ 'Success' if secrets_restore_result is success else 'Failed' }}
          - Containers pull/tag: {{ 'Success' if (pull_result is defined and pull_result is success) and (tag_result is defined and tag_result is success) else ('Skipped' if not containers_file.stat.exists else 'Partial - Some errors occurred') }}
          - Volumes: {{ volumes_status }}
          - Containers running: {{ containers_running_status }}
          
          Overall status: {{ 'SUCCESS' if install_status == 'Success' and containers_running_status == 'Success' and service_start_result is success else 'PARTIAL - See details above' }}
          
          A detailed status report has been saved to: /tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status

    - name: Read cleanup instructions from status file
      command: cat "/tmp/lme_rollback_{{ ansible_date_time.iso8601_basic_short }}.status"
      register: status_file
      changed_when: false

    - name: Show cleanup instructions
      debug:
        msg: "{{ dict(range(1, status_file.stdout_lines|length + 1) | zip(status_file.stdout_lines)) }}" 