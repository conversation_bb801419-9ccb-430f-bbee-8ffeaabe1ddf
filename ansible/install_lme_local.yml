---
- name: Setup /opt/lme, check sudo, and configure other required directories/files
  hosts: localhost
  connection: local
  become: no  # Default to no privilege escalation
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
    install_user: "root"
    storage_runroot: "{{ storage_runroot | default('/run/containers/storage') }}"
    storage_graphroot: "{{ storage_graphroot | default('/var/lib/containers/storage') }}"
  tags: ['base', 'all']

  tasks:
    - name: Expand clone_directory path
      set_fact:
        clone_directory: "{{ clone_directory | expanduser }}"

    - name: Ensure /opt/lme directory exists
      file:
        path: /opt/lme
        state: directory
        owner: "{{ install_user }}"
        group: "{{ install_user }}"
        mode: '0700'
      become: yes

    - name: Check if /opt/lme/lme-environment.env exists
      stat:
        path: "/opt/lme/lme-environment.env"
      register: optenv_file 
      become: yes

    - name: Check if lme-environment.env exists
      stat:
        path: "{{ clone_directory }}/config/lme-environment.env"
      register: env_file

    - name: Fail if lme-environment.env doesn't exist (either in ./config OR /opt/lme)
      fail:
        msg: "lme-environment.env file not found in {{ clone_directory }}/config/. Please copy example.env to lme-environment.env in the config directory and edit it before running this playbook."
      when: 
        -  (not optenv_file.stat.exists) and (not env_file.stat.exists)

    - name: Copy lme-environment.env to /opt/lme (only if it doesn't exist)
      command: "mv {{ clone_directory }}/config/lme-environment.env /opt/lme/lme-environment.env"
      become: yes
      when: not optenv_file.stat.exists

    - name: Set correct permissions for lme-environment.env
      file:
        path: /opt/lme/lme-environment.env
        owner: "{{ install_user }}"
        group: "{{ install_user }}"
        mode: '0600'
      become: yes

    - name: Check sudo setup
      command: sudo -n true
      register: sudo_check
      ignore_errors: yes
      changed_when: false

    - name: Display sudo information
      debug:
        msg: "{{ 'Passwordless sudo is available.' if sudo_check.rc == 0 else 'Sudo will require a password for privileged operations.' }}"

    - name: Ensure sudo access
      command: sudo -n true
      changed_when: false

    - name: Setup /etc/subgid
      lineinfile:
        path: "/etc/subuid"
        line: 'containers:165536:65536'
        create: yes
      become: yes

    - name: Setup /etc/subgid
      lineinfile:
        path: "/etc/subgid"
        line: 'containers:165536:65536'
        create: yes
      become: yes

- name: Setup password information
  hosts: localhost
  gather_facts: no
  become: yes
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
    install_user: "root"

    #Requirements, set as variables to enable testing or setting if a user desires
    ansible_vault_password: ""
    min_length: 12
          
    #directories
    user_config_dir: "/root/.config/containers"
    user_secrets_conf: "/root/.config/containers/containers.conf"  # Update with actual path
    user_storage_conf: "/root/.config/containers/storage.conf"  # Update with actual path
    global_storage_conf: "/etc/containers/storage.conf"  # Update with actual path
    config_dir: "/etc/lme"
    user_vault_dir: "/etc/lme/vault"  # Update with actual path
    password_file: "/etc/lme/pass.sh"
  tags: ['base', 'all']

  tasks:
    - name: Create Vault password
      shell: |
        password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30)
        while ! echo -n $password | grep -Eq '[$@!]+';do password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30);done
        echo -n $password
      args:
        executable: /bin/bash
      register: vault_password

    - name: Set ANSIBLE_VAULT_PASSWORD
      set_fact:
        ansible_vault_password: "{{ vault_password.stdout }}"

    #TODO: make this a bit nicer
    - name: Check password complexity
      block:
        - name: Check password length
          assert:
            that:
              - ansible_vault_password | length >= min_length
            fail_msg: "Input is too short. It should be at least {{ min_length }} characters long."

        - name: Generate SHA-1 hash of the password
          shell: "echo -n '{{ ansible_vault_password }}' | openssl sha1 | awk '{print $2}'"
          args: 
            executable: /bin/bash
          register: password_hash

        - name: Set prefix and suffix
          set_fact:
            prefix: "{{ password_hash.stdout[0:5] }}"
            suffix: "{{ password_hash.stdout[5:] }}"

        - name: Check against HIBP API
          uri:
            url: "https://api.pwnedpasswords.com/range/{{ prefix }}"
            method: GET
            return_content: yes
          register: hibp_response

        - name: Fail if password is found in breaches
          fail:
            msg: "The password has been found in breaches... this should only happen if you provided a password via the cli... choose a different password"
          when: hibp_response.content | regex_search(suffix)

    - name: Create global config directory
      file:
        path: "{{ config_dir }}"
        state: directory
        mode: '0700'

    - name: Create user config directory
      file:
        path: "{{ user_config_dir }}"
        state: directory
        mode: '0700'

    - name: Create user vault directory
      file:
        path: "{{ user_vault_dir }}"
        state: directory
        mode: '0700'

    - name: check if vault-pass.sh is created
      stat:
        path: "{{ password_file }}"
      register: pass_file
      become: yes

    - name: Create vault-pass.sh with secure permissions (only if it doesn't exist!)
      copy:
        dest: "{{ password_file }}"
        content: |
          #!/bin/bash
          echo "{{ ansible_vault_password }}"
        mode: '0700'
      when: not pass_file.stat.exists

    - name: Ensure ANSIBLE_VAULT_PASSWORD_FILE is set in .profile
      lineinfile:
        path: /root/.profile
        line: "export ANSIBLE_VAULT_PASSWORD_FILE=\"{{ password_file }}\""
        state: present

    - name: Setup Podman secrets configuration
      copy:
        dest: "{{ user_secrets_conf }}"
        content: |
          [secrets]
          driver = "shell"

          [secrets.opts]
          list = "ls {{ user_vault_dir }}"
          lookup = "ansible-vault view {{ user_vault_dir }}/$SECRET_ID | tr -d '\n'"
          store = "cat > {{ user_vault_dir }}/$SECRET_ID && chmod 700 {{ user_vault_dir }}/$SECRET_ID && ansible-vault encrypt {{ user_vault_dir }}/$SECRET_ID"
          delete = "rm {{ user_vault_dir }}/$SECRET_ID"
        mode: '0600'
    - name: setup root overlay-fs usage
      copy:
        dest: "{{ user_storage_conf }}"
        content: |
          [storage]
          driver = "overlay"

          [storage.options.overlay]
          mount_program = "/usr/bin/fuse-overlayfs"

        mode: '0600'

    - name: Create /etc/containers
      file:
        path: /etc/containers/
        state: directory
        owner: "root"
        group: "root"
        mode: '0744'

    - name: setup global overlay-fs usage
      copy:
        dest: "{{ global_storage_conf }}"
        content: |
          [storage]
          driver = "overlay"
          runroot = "{{ storage_runroot | default('/run/containers/storage') }}"
          graphroot = "{{ storage_graphroot | default('/var/lib/containers/storage') }}"
          [storage.options.overlay]
          mount_program = "/usr/bin/fuse-overlayfs"

        mode: '0600'

- name: Setup Nix
  hosts: localhost
  connection: local
  become: no
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
    install_user: "{{ ansible_user_id }}"
  tags: ['base', 'all']
  tasks:
    - name: Update apt cache
      apt:
        update_cache: yes
      become: yes

    - name: Install required packages
      apt:
        name:
          - jq
          - uidmap
          - nix-bin
          - nix-setup-systemd
          - python3-pexpect
          - fuse-overlayfs
        state: present
      become: yes

    - name: Add Nix channel
      command: nix-channel --add https://nixos.org/channels/nixpkgs-unstable nixpkgs
      become: yes

    - name: Update Nix channel
      command: nix-channel --update
      become: yes

    - name: Add user to nix-users group
      user:
        name: "{{ install_user }}"
        groups: nix-users
        append: yes
      become: yes

    - name: Update PATH for Ansible execution
      set_fact:
        ansible_env: "{{ ansible_env | combine({'PATH': ansible_env.PATH ~ ':/nix/var/nix/profiles/default/bin'}) }}"

    - name: Update PATH in user's profile
      lineinfile:
        path: "~/.profile"
        line: 'export PATH=$PATH:/nix/var/nix/profiles/default/bin'
        create: yes

    - name: Update PATH in root's profile 
      lineinfile:
        path: "/root/.profile"
        line: 'export PATH=$PATH:/nix/var/nix/profiles/default/bin'
        create: yes
      become: yes

    - name: Update PATH in user's bashrc
      lineinfile:
        path: "~/.bashrc"
        line: 'export PATH=$PATH:/nix/var/nix/profiles/default/bin'
        create: yes

    - name: Update PATH in root's bashrc
      lineinfile:
        path: "/root/.bashrc"
        line: 'export PATH=$PATH:/nix/var/nix/profiles/default/bin'
        create: yes
      become: yes

- name: Setup Podman
  hosts: localhost
  connection: local
  become: no
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
    install_user: "{{ ansible_user_id }}"
  tags: ['system', 'all']

  handlers:
    - name: restart nix-daemon
      systemd:
        name: nix-daemon
        state: restarted
        daemon_reload: yes
      become: yes
  tasks:
    - name: Ensure Nix daemon is running
      systemd:
        name: nix-daemon
        state: started
        enabled: yes
      become: yes
      notify: restart nix-daemon

    - name: Wait for Nix daemon to be ready
      wait_for:
        timeout: 10
      when: ansible_play_hosts_all.index(inventory_hostname) == 0

    - name: Install Podman using Nix
      command: nix-env -iA nixpkgs.podman
      become: yes
      environment:
        PATH: "{{ ansible_env.PATH }}"
      register: podman_install
      retries: 3
      delay: 5
      until: podman_install is not failed

    - name: Set sysctl limits 
      command: "{{ clone_directory }}/scripts/set_sysctl_limits.sh"
      environment:
        NON_ROOT_USER: "{{ install_user }}"
      become: yes

    - name: Link latest podman quadlet
      command: "{{ clone_directory }}/scripts/link_latest_podman_quadlet.sh"
      become: yes

- name: set service user passwords
  hosts: localhost
  connection: local
  become: no  # Default to no privilege escalation
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
  tags: ['system', 'all']
  tasks:
    #maybe check for each in the shell script below?
    - name: Register a variable, ignore errors and continue
      shell: |
        source /root/.profile 
        podman secret ls | grep -q elastic
      register: result
      become: yes
      args:
        executable: /bin/bash
      ignore_errors: true
      #only fail on a real error
      failed_when: result.rc != 0 and (result.rc == 1 and result.changed == false)
      
    - name: Set podman secret passwords
      shell: |
        source /root/.profile 
        password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30)
        while ! echo -n $password | grep -Eq '[$@!]+';do password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30); echo $password;done
        echo -n $password | podman secret create --driver shell --replace "{{ item }}" -
      args:
        executable: /bin/bash
      loop: 
        - elastic
        - kibana_system
        - wazuh_api
        - wazuh
      become: yes
      ## only run this when
      when: result.rc == 1 

- name: Install Quadlets
  hosts: localhost
  connection: local
  become: no  # Default to no privilege escalation
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
    install_user: "{{ ansible_user_id }}"
  tags: ['system', 'all']
  tasks:
    - name: Enable linger for user
      command: "loginctl enable-linger {{ install_user }}"
      become: yes

    - name: Copy config files /opt/lme/config
      copy:
        src: "{{ clone_directory }}/config/"
        dest: /opt/lme/config/
        owner: "{{ install_user }}"
        group: "{{ install_user }}"
        mode: '0644'
      become: yes

    - name: Create /etc/containers/systemd
      file:
        path: /opt/containers/systemd
        state: directory
        owner: "root"
        group: "root"
        mode: '0744'
      become: yes

    - name: Copy quadlet files to /etc/containers/systemd 
      copy:
        src: "{{ clone_directory }}/quadlet/"
        dest: /etc/containers/systemd/
        owner: "root"
        group: "root"
        mode: '0644'
      become: yes

    - name: copy lme.service to /etc/systemd/system
      copy:
        src: "{{ clone_directory }}/quadlet/lme.service"
        dest: "/etc/systemd/system/lme.service"
        owner: "root"
        group: "root"
        mode: '0644'
      become: yes

    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes
      become: yes

- name: Setup Containers for root
  hosts: localhost
  connection: local
  become: no
  vars:
    clone_directory: "{{ clone_dir | default('~/LME') }}"
  tags: ['system', 'all']
  tasks:
    - name: Add Container Image policy file
      copy:
        content: |
          {
              "default": [
                  {
                      "type": "insecureAcceptAnything"
                  }
              ]
          }
        dest: /etc/containers/policy.json
      become: yes

    - name: Pull containers
      shell: |
        export PATH=$PATH:/nix/var/nix/profiles/default/bin
        podman pull {{ item }}
      args:
        executable: /bin/bash
      loop: "{{ lookup('file', clone_directory + '/config/containers.txt').splitlines() }}"
      become: yes

    - name: Tag containers
      shell: |
        export PATH=$PATH:/nix/var/nix/profiles/default/bin
        podman image tag {{ item }} {{ item.split('/')[-1].split(':')[0] }}:LME_LATEST
      args:
        executable: /bin/bash
      loop: "{{ lookup('file', clone_directory + '/config/containers.txt').splitlines() }}"
      become: yes

- name: Start lme.service
  hosts: localhost
  connection: local
  become: yes  # Default to no privilege escalation
  tags: ['system', 'all']
  tasks:
    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes
      become: yes

    - name: Start LME service
      systemd:
        name: lme.service
        state: started
        enabled: yes
      become: yes
