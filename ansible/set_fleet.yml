---
- name: Set up Fleet
  hosts: localhost
  become: yes
  gather_facts: no

  vars:
    headers:
      kbn-version: "8.15.3"
      kbn-xsrf: "kibana"
      Content-Type: "application/json"
    max_retries: 60
    delay_seconds: 10
    debug_mode: false

  tasks:
    - name: Read lme-environment.env file
      ansible.builtin.slurp:
        src: /opt/lme/lme-environment.env
      register: lme_env_content

    - name: Set environment variables
      ansible.builtin.set_fact:
        env_dict: "{{ env_dict | default({}) | combine({ item.split('=', 1)[0]: item.split('=', 1)[1] }) }}"
      loop: "{{ (lme_env_content['content'] | b64decode).split('\n') }}"
      when: item != '' and not item.startswith('#')

    - name: Display set environment variables
      debug:
        msg: "Set {{ item.key }}"
      loop: "{{ env_dict | dict2items }}"
      when: item.value | length > 0

    - name: Source extract_secrets
      ansible.builtin.shell: |
        set -a
        . {{ playbook_dir }}/../scripts/extract_secrets.sh -q
        echo "elastic=$elastic"
        echo "wazuh=$wazuh"
        echo "kibana_system=$kibana_system"
        echo "wazuh_api=$wazuh_api"
      args:
        executable: /bin/bash
      register: extract_secrets_vars
      no_log: "{{ not debug_mode }}"

    - name: Set secret variables
      ansible.builtin.set_fact:
        env_dict: "{{ env_dict | combine({ item.split('=', 1)[0]: item.split('=', 1)[1] }) }}"
      loop: "{{ extract_secrets_vars.stdout_lines }}"
      no_log: "{{ not debug_mode }}"

    - name: Set playbook variables
      ansible.builtin.set_fact:
        ipvar: "{{ env_dict.IPVAR | default('') }}"
        local_kbn_url: "{{ env_dict.LOCAL_KBN_URL | default('') }}"
        local_es_url: "{{ env_dict.LOCAL_ES_URL | default('') }}"
        stack_version: "{{ env_dict.STACK_VERSION | default('') }}"
        cluster_name: "{{ env_dict.CLUSTER_NAME | default('') }}"
        elastic_username: "{{ env_dict.ELASTIC_USERNAME | default('') }}"
        elasticsearch_username: "{{ env_dict.ELASTICSEARCH_USERNAME | default('') }}"
        kibana_fleet_username: "{{ env_dict.KIBANA_FLEET_USERNAME | default('') }}"
        indexer_username: "{{ env_dict.INDEXER_USERNAME | default('') }}"
        api_username: "{{ env_dict.API_USERNAME | default('') }}"
        license: "{{ env_dict.LICENSE | default('') }}"
        es_port: "{{ env_dict.ES_PORT | default('') }}"
        kibana_port: "{{ env_dict.KIBANA_PORT | default('') }}"
        fleet_port: "{{ env_dict.FLEET_PORT | default('') }}"
        mem_limit: "{{ env_dict.MEM_LIMIT | default('') }}"
        elastic_password: "{{ env_dict.elastic | default('') }}"
        wazuh_password: "{{ env_dict.wazuh | default('') }}"
        kibana_system_password: "{{ env_dict.kibana_system | default('') }}"
        wazuh_api_password: "{{ env_dict.wazuh_api | default('') }}"

    - name: Debug - Display set variables (sensitive information redacted)
      debug:
        msg:
          - "ipvar: {{ ipvar }}"
          - "local_kbn_url: {{ local_kbn_url }}"
          - "local_es_url: {{ local_es_url }}"
          - "elastic_username: {{ elastic_username }}"
          - "stack_version: {{ stack_version }}"
          - "cluster_name: {{ cluster_name }}"
          - "elasticsearch_username: {{ elasticsearch_username }}"
          - "kibana_fleet_username: {{ kibana_fleet_username }}"
          - "indexer_username: {{ indexer_username }}"
          - "api_username: {{ api_username }}"
          - "license: {{ license }}"
          - "es_port: {{ es_port }}"
          - "kibana_port: {{ kibana_port }}"
          - "fleet_port: {{ fleet_port }}"
          - "mem_limit: {{ mem_limit }}"
          - "elastic password is set: {{ elastic_password | length > 0 }}"
          - "wazuh password is set: {{ wazuh_password | length > 0 }}"
          - "kibana_system password is set: {{ kibana_system_password | length > 0 }}"
          - "wazuh_api password is set: {{ wazuh_api_password | length > 0 }}"
      when: debug_mode | bool

    - name: Wait for Kibana port to be available
      wait_for:
        host: "{{ ipvar }}"
        port: "{{ kibana_port | int }}"
        timeout: 300
      register: kibana_port_check

    - name: Wait for Fleet API to be ready
      ansible.builtin.shell: |
        attempt=0
        max_attempts=30
        delay=10
        while [ $attempt -lt $max_attempts ]; do
          response=$(curl -s -o /dev/null -w "%{http_code}" -kL -u elastic:{{ elastic_password }} {{ local_kbn_url }}/api/fleet/agents/setup)
          if [ "$response" = "200" ]; then
            echo "Fleet API is ready. Proceeding with configuration..."
            exit 0
          fi
          echo "Waiting for Fleet API to be ready..."
          sleep $delay
          attempt=$((attempt+1))
        done
        echo "Fleet API did not become ready within the expected time."
        exit 1
      register: fleet_api_check
      changed_when: false
      no_log: "{{ not debug_mode }}"

    - name: Display Fleet API check result
      debug:
        var: fleet_api_check.stdout_lines

    - name: Confirm Fleet API is ready
      debug:
        msg: "Fleet API is ready"
      when: "'Fleet API is ready' in fleet_api_check.stdout"

    - name: Fail if Fleet API is not ready
      fail:
        msg: "Fleet API did not become ready within the expected time."
      when: "'Fleet API is ready' not in fleet_api_check.stdout"

    - name: Get CA fingerprint
      ansible.builtin.shell: |
        sudo bash -c '
          set -a
          . {{ playbook_dir }}/../scripts/extract_secrets.sh -q
          set +a
          /nix/var/nix/profiles/default/bin/podman exec -w /usr/share/elasticsearch/config/certs/ca lme-elasticsearch cat ca.crt | openssl x509 -noout -fingerprint -sha256 | cut -d "=" -f 2 | tr -d : | head -n1
        '
      register: ca_fingerprint
      changed_when: false
      become: yes
      become_method: sudo
      no_log: "{{ not debug_mode }}"

    - name: Display CA fingerprint
      debug:
        var: ca_fingerprint.stdout
      when:
        - ca_fingerprint is defined
        - ca_fingerprint.stdout is defined
        - debug_mode | bool

    - name: Set Fleet server hosts
      uri:
        url: "{{ local_kbn_url }}/api/fleet/settings"
        method: PUT
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        body:
          fleet_server_hosts: ["https://{{ ipvar }}:{{ fleet_port }}"]
      register: fleet_server_hosts_result
      no_log: "{{ not debug_mode }}"
      ignore_errors: yes

    - name: Debug Fleet server hosts result
      debug:
        var: fleet_server_hosts_result
      when: fleet_server_hosts_result is defined and debug_mode | bool

    - name: Set Fleet default output hosts
      uri:
        url: "{{ local_kbn_url }}/api/fleet/outputs/fleet-default-output"
        method: PUT
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        body:
          hosts: ["https://{{ ipvar }}:9200"]
      register: fleet_output_hosts_result
      no_log: "{{ not debug_mode }}"
      ignore_errors: yes

    - name: Debug Fleet default output hosts result
      debug:
        var: fleet_output_hosts_result
      when: fleet_output_hosts_result is defined

    - name: Set Fleet default output CA trusted fingerprint
      uri:
        url: "{{ local_kbn_url }}/api/fleet/outputs/fleet-default-output"
        method: PUT
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        body:
          ca_trusted_fingerprint: "{{ ca_fingerprint.stdout }}"
      register: fleet_output_fingerprint_result
      no_log: "{{ not debug_mode }}"

    - name: Set Fleet default output SSL verification mode
      uri:
        url: "{{ local_kbn_url }}/api/fleet/outputs/fleet-default-output"
        method: PUT
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        body:
          config_yaml: "ssl.verification_mode: certificate"
      register: fleet_output_ssl_result
      no_log: "{{ not debug_mode }}"

    - name: Create Endpoint Policy
      uri:
        url: "{{ local_kbn_url }}/api/fleet/agent_policies?sys_monitoring=true"
        method: POST
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        body:
          name: "Endpoint Policy"
          description: ""
          namespace: "default"
          monitoring_enabled: ["logs", "metrics"]
          inactivity_timeout: 1209600
        timeout: 600
      register: endpoint_policy_result
      no_log: "{{ not debug_mode }}"

    - name: Get Endpoint package version
      uri:
        url: "{{ local_kbn_url }}/api/fleet/epm/packages/endpoint"
        method: GET
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
      register: endpoint_package_result
      no_log: "{{ not debug_mode }}"

    - name: Create Elastic Defend package policy
      uri:
        url: "{{ local_kbn_url }}/api/fleet/package_policies"
        method: POST
        user: "{{ elastic_username }}"
        password: "{{ elastic_password }}"
        force_basic_auth: yes
        validate_certs: no
        headers: "{{ headers }}"
        body_format: json
        timeout: 600
        body:
          name: "Elastic Defend"
          description: ""
          namespace: "default"
          policy_id: "{{ endpoint_policy_result.json.item.id }}"
          enabled: true
          inputs:
            - enabled: true
              streams: []
              type: "ENDPOINT_INTEGRATION_CONFIG"
              config:
                _config:
                  value:
                    type: "endpoint"
                    endpointConfig:
                      preset: "EDRComplete"
          package:
            name: "endpoint"
            title: "Elastic Defend"
            version: "{{ endpoint_package_result.json.item.version }}"
      register: elastic_defend_policy_result
      no_log: "{{ not debug_mode }}"

    - name: Display results
      debug:
        var: "{{ item }}"
      loop:
        - fleet_server_hosts_result
        - fleet_output_hosts_result
        - fleet_output_fingerprint_result
        - fleet_output_ssl_result
        - endpoint_policy_result
        - elastic_defend_policy_result
