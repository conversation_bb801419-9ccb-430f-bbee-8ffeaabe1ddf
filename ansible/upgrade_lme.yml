---
- name: Upgrade LME
  hosts: localhost
  connection: local
  become: yes
  vars:
    # Common variables
    clone_directory: "{{ clone_dir | default(playbook_dir + '/..') }}"
    install_user: "{{ ansible_user_id }}"
    new_lme_version: "2.1.0"
    
    # Directory configuration
    config_dir: "/etc/lme"
    env_file_path: "/opt/lme/lme-environment.env"
    
    # Elasticsearch configuration
    es_port: 9200
    kibana_port: 5601
    fleet_port: 8220
    
    # Container versions - will be derived from containers.txt
    containers_file: "{{ playbook_dir }}/../config/containers.txt"
    
  tasks:
    - name: Check if config containers file exists
      stat:
        path: "{{ containers_file }}"
      register: containers_file_stat
      
    - name: Fail if containers file doesn't exist
      fail:
        msg: "Containers file not found at {{ containers_file }}"
      when: not containers_file_stat.stat.exists
      
    - name: Read container versions from file
      set_fact:
        container_versions: "{{ lookup('file', containers_file).splitlines() }}"
      
    - name: Display container file contents
      debug:
        msg: "Container file contents: {{ container_versions }}"
        
    - name: Verify container file format
      fail:
        msg: |
          The containers file at {{ containers_file }} does not contain the required container definitions.
          Expected to find containers for elasticsearch, kibana, wazuh, and elastalert.
          Please check the file format and contents.
          
          Current contents:
          {% for container in container_versions %}
          - {{ container }}
          {% endfor %}
      when: >
        container_versions | select('search', 'elasticsearch') | list | length == 0 or
        container_versions | select('search', 'kibana') | list | length == 0 or
        container_versions | select('search', 'wazuh') | list | length == 0 or
        container_versions | select('search', 'elastalert') | list | length == 0
        
    - name: Determine podman command location
      shell: |
        # Check multiple locations for podman
        if command -v podman >/dev/null 2>&1; then
          echo "podman"
        elif [ -x "/nix/var/nix/profiles/default/bin/podman" ]; then
          echo "/nix/var/nix/profiles/default/bin/podman"
        elif [ -x "/usr/local/bin/podman" ]; then
          echo "/usr/local/bin/podman"
        else
          echo "ERROR: podman command not found in any expected location" >&2
          echo "Checked locations:" >&2
          echo "- Standard PATH: $(which podman 2>/dev/null || echo 'not found')" >&2
          echo "- Nix location: /nix/var/nix/profiles/default/bin/podman" >&2
          echo "- Symlink location: /usr/local/bin/podman" >&2
          exit 1
        fi
      args:
        executable: /bin/bash
      environment:
        PATH: "{{ ansible_env.PATH }}:/nix/var/nix/profiles/default/bin"
      register: podman_cmd_result
      
    - name: Check if podman symlink exists
      stat:
        path: "/usr/local/bin/podman"
      register: podman_symlink_stat
      
    - name: Check if nix podman exists
      stat:
        path: "/nix/var/nix/profiles/default/bin/podman"
      register: nix_podman_stat
      
    - name: Create podman symlink if needed and nix podman exists
      file:
        src: "/nix/var/nix/profiles/default/bin/podman"
        dest: "/usr/local/bin/podman"
        state: link
        force: yes
      when: 
        - not podman_symlink_stat.stat.exists
        - nix_podman_stat.stat.exists
      ignore_errors: yes
      
    - name: Set podman command variable
      set_fact:
        podman_cmd: "{{ podman_cmd_result.stdout }}"
        
    - name: Display podman command location
      debug:
        msg: "Using podman from: {{ podman_cmd }}"
        
    - name: Extract container versions
      set_fact:
        elasticsearch_image: "{{ container_versions | select('search', 'elasticsearch') | first }}"
        kibana_image: "{{ container_versions | select('search', 'kibana') | first }}"
        wazuh_image: "{{ container_versions | select('search', 'wazuh') | first }}"
        elastalert_image: "{{ container_versions | select('search', 'elastalert') | first }}"
        
    - name: Extract version numbers
      set_fact:
        elasticsearch_version: "{{ elasticsearch_image | regex_replace('^.*:(.*)', '\\1') }}"
        kibana_version: "{{ kibana_image | regex_replace('^.*:(.*)', '\\1') }}"
        wazuh_version: "{{ wazuh_image | regex_replace('^.*:(.*)', '\\1') }}"
        elastalert_version: "{{ elastalert_image | regex_replace('^.*:(.*)', '\\1') }}"
        
    - name: Display container versions
      debug:
        msg: |
          Container versions from {{ containers_file }}:
          - Elasticsearch: {{ elasticsearch_version }} ({{ elasticsearch_image }})
          - Kibana: {{ kibana_version }} ({{ kibana_image }})
          - Wazuh: {{ wazuh_version }} ({{ wazuh_image }})
          - ElastAlert: {{ elastalert_version }} ({{ elastalert_image }})
        
    - name: Check if environment file exists
      stat:
        path: "{{ env_file_path }}"
      register: env_file
      
    - name: Fail if environment file doesn't exist
      fail:
        msg: "Environment file not found at {{ env_file_path }}"
      when: not env_file.stat.exists
      
    - name: Read environment file
      include_tasks: tasks/load_env.yml
      
    - name: Set current LME version variable
      set_fact:
        current_lme_version: "{{ env_dict.LME_VERSION | default('2.0.0') }}"

    - name: Display current version information
      debug:
        msg: "Current LME version: {{ current_lme_version }}, target version: {{ new_lme_version }}"

    - name: Check upgrade requirements
      block:
        - name: Check if upgrade is needed
          shell: |
            if [[ "{{ current_lme_version }}" == "Not set" || "{{ current_lme_version }}" == "2.0.0" ]]; then
              # No version or unset version, upgrade needed
              echo "Upgrade needed: No current version set"
              exit 0
            else
              # Compare versions using sort -V (natural version sort)
              if [[ "{{ current_lme_version }}" == "{{ new_lme_version }}" ]]; then
                # Same version - no upgrade needed
                exit 2
              elif [[ "$(echo -e "{{ current_lme_version }}\n{{ new_lme_version }}" | sort -V | head -n 1)" == "{{ current_lme_version }}" ]]; then
                # Current version is lower than new version
                echo "Upgrade needed: Current version {{ current_lme_version }} is lower than new version {{ new_lme_version }}"
                exit 0
              else
                # Current version is higher - downgrade not allowed
                echo "ERROR: Downgrade not allowed. Current version {{ current_lme_version }} is higher than requested version {{ new_lme_version }}"
                exit 3
              fi
            fi
          args:
            executable: /bin/bash
          register: upgrade_check
          changed_when: false
          failed_when: upgrade_check.rc == 3
          
        - name: Print nice message for no upgrade needed
          debug:
            msg: "No upgrade needed: Current version ({{ current_lme_version }}) already matches target version ({{ new_lme_version }})"
          when: upgrade_check.rc == 2
          
        - name: End playbook if no upgrade needed
          meta: end_play
          when: upgrade_check.rc == 2

      rescue:
        - name: Fail with clear downgrade message
          fail:
            msg: "ERROR: Downgrade not allowed. Current version {{ current_lme_version }} is higher than requested version {{ new_lme_version }}"

    - name: Get valid backup choice
      pause:
        prompt: |
          {% if backup_choice_loop is defined and backup_choice_loop.user_input is defined %}
          Invalid input '{{ backup_choice_loop.user_input }}'. Please enter y/yes or n/no.
          {% endif %}
          Do you want to create a backup before upgrading? (y/yes/n/no)
          This is highly recommended in case you need to rollback.
      register: backup_choice_loop
      until: backup_choice_loop.user_input | lower in ['y', 'yes', 'n', 'no']
      retries: 999
      delay: 0
      when: skip_prompts is not defined or not skip_prompts | bool
      
    - name: Set backup choice (interactive)
      set_fact:
        backup_choice: "{{ backup_choice_loop.user_input | lower }}"
      when: skip_prompts is not defined or not skip_prompts | bool
      
    - name: Set backup choice (automated - no backup)
      set_fact:
        backup_choice: "n"
      when: skip_prompts is defined and skip_prompts | bool
      
    - name: Display backup in progress message
      debug:
        msg: "Starting LME backup process before upgrade. This may take a few minutes..."
      when: backup_choice in ['y', 'yes']
        
    - name: Run backup
      include_role:
        name: backup_lme
      vars:
        skip_prompts: true  # Skip prompts during automated backup
        skip_service_restart: true  # Leave services stopped for upgrade operations
      when: backup_choice in ['y', 'yes']

    - name: Stop LME service when backup is skipped
      systemd:
        name: lme
        state: stopped
      when: backup_choice in ['n', 'no']
      
    - name: Stop LME service when backup was done
      systemd:
        name: lme
        state: stopped
      when: backup_choice in ['y', 'yes']
      
    - name: Wait for containers to stop
      shell: |
        {{ podman_cmd }} ps -a --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true
      args:
        executable: /bin/bash
      register: running_containers
      until: running_containers.stdout_lines | length == 0
      retries: 12
      delay: 5
      ignore_errors: yes
    
    - name: Copy containers.txt to /opt/lme/config
      copy:
        src: "{{ clone_directory }}/config/containers.txt"
        dest: "/opt/lme/config/containers.txt"
        remote_src: yes
      register: containers_copied
        
    - name: Update STACK_VERSION in environment file
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^STACK_VERSION="
        line: "STACK_VERSION={{ elasticsearch_version }}"
      register: stack_version_updated
      
    - name: Set FLEET_ENROLL=1 for upgrade enrollment
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^FLEET_ENROLL="
        line: "FLEET_ENROLL=1"
        insertafter: EOF
        state: present
      register: fleet_enroll_updated
        
    - name: Set FLEET_SERVER_ENABLE=1 for upgrade enrollment
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^FLEET_SERVER_ENABLE="
        line: "FLEET_SERVER_ENABLE=1"
        insertafter: EOF
        state: present
      register: fleet_server_enable_updated
        
    - name: Set KIBANA_FLEET_SETUP=1 for upgrade enrollment
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^KIBANA_FLEET_SETUP="
        line: "KIBANA_FLEET_SETUP=1"
        insertafter: EOF
        state: present
      register: kibana_fleet_setup_updated
        
    - name: Pull new container images
      shell: |
        {{ podman_cmd }} pull {{ item }}
      args:
        executable: /bin/bash
      loop: "{{ lookup('file', clone_directory + '/config/containers.txt').splitlines() }}"
      register: pull_result
      retries: 3
      delay: 5
      until: pull_result is not failed
      ignore_errors: yes
      
    - name: Display container pull errors
      debug:
        msg: "Failed to pull container: {{ item.item }}"
      loop: "{{ pull_result.results }}"
      when: item is failed
      loop_control:
        label: "{{ item.item }}"
        
    - name: Tag new containers
      shell: |
        {{ podman_cmd }} image tag {{ item }} {{ item.split('/')[-1].split(':')[0] }}:LME_LATEST
      args:
        executable: /bin/bash
      loop: "{{ lookup('file', clone_directory + '/config/containers.txt').splitlines() }}"
      register: tag_result
      retries: 3
      delay: 5
      until: tag_result is not failed
      ignore_errors: yes
      
    - name: Display container tag errors
      debug:
        msg: "Failed to tag container: {{ item.item }}"
      loop: "{{ tag_result.results }}"
      when: item is failed
      loop_control:
        label: "{{ item.item }}"
        
    - name: Remove fleet-server data volume to prevent version mismatch
      shell: |
        echo "Removing lme_fleet_data volume to ensure fleet-server uses fresh container binaries..."
        {{ podman_cmd }} volume rm lme_fleet_data || true
      args:
        executable: /bin/bash
      register: volume_removal_result
      ignore_errors: yes
      
    - name: Display fleet volume removal result
      debug:
        msg: |
          Fleet data volume removal: {{ 'SUCCESS' if volume_removal_result.rc == 0 else 'INFO - Volume may not exist or already removed' }}
          This ensures fleet-server will use the correct container version binaries.
        
    - name: Create backup directory for quadlet files
      file:
        path: "/opt/lme/backup/quadlet-{{ ansible_date_time.epoch }}"
        state: directory
        owner: root
        group: root
        mode: '0755'
      register: quadlet_backup_dir
      
    - name: Backup existing quadlet files
      shell: |
        if [ -d "/etc/containers/systemd" ]; then
          cp -r /etc/containers/systemd/* {{ quadlet_backup_dir.path }}/ 2>/dev/null || true
          echo "Backed up existing quadlet files to {{ quadlet_backup_dir.path }}"
        else
          echo "No existing quadlet directory found"
        fi
      args:
        executable: /bin/bash
      register: quadlet_backup_result
      
    - name: Display quadlet backup result
      debug:
        msg: "{{ quadlet_backup_result.stdout }}"
        
    - name: Create /etc/containers/systemd directory
      file:
        path: /etc/containers/systemd
        state: directory
        owner: root
        group: root
        mode: '0744'
        
    - name: Copy updated quadlet files to /etc/containers/systemd
      copy:
        src: "{{ clone_directory }}/quadlet/"
        dest: /etc/containers/systemd/
        owner: root
        group: root
        mode: '0644'
      register: quadlet_copy_result
      
    - name: Copy lme.service to /etc/systemd/system
      copy:
        src: "{{ clone_directory }}/quadlet/lme.service"
        dest: "/etc/systemd/system/lme.service"
        owner: root
        group: root
        mode: '0644'
      register: lme_service_copy_result
      
    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes
      register: daemon_reload_result
      
    - name: Display quadlet update results
      debug:
        msg: |
          Quadlet files updated successfully:
          - Backup location: {{ quadlet_backup_dir.path }}
          - Quadlet files: {{ 'Updated' if quadlet_copy_result.changed else 'No changes needed' }}
          - LME service file: {{ 'Updated' if lme_service_copy_result.changed else 'No changes needed' }}
          - Systemd daemon: {{ 'Reloaded' if daemon_reload_result.changed else 'Already current' }}
        
    - name: Restart LME service
      systemd:
        name: "lme"
        state: restarted
      tags: ['system']
      
    - name: Wait for containers to start
      shell: |
        {{ podman_cmd }} ps --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true
      args:
        executable: /bin/bash
      register: started_containers
      until: started_containers.stdout_lines | length > 0
      retries: 12
      delay: 5
      ignore_errors: yes
      
    - name: Wait for services to be ready
      pause:
        seconds: 60
        
    - name: Verify services are running
      shell: |
        echo "Using podman from: {{ podman_cmd }}"
        
        LME_CONTAINERS=$({{ podman_cmd }} ps --format "{{ '{{' }}.Names{{ '}}' }}" | grep -E "lme" || true)
        CONTAINER_COUNT=$(echo "$LME_CONTAINERS" | wc -l)
        echo "$LME_CONTAINERS"
        
        if [ $CONTAINER_COUNT -ge 5 ]; then
          echo "All expected LME services are running"
          exit 0
        else
          echo "Only $CONTAINER_COUNT of 5 expected LME services are running"
          exit 1
        fi
      args:
        executable: /bin/bash
      register: containers_status
      until: containers_status.rc == 0
      retries: 15
      delay: 10
      
    - name: Display running containers
      debug:
        msg: "{{ containers_status.stdout_lines }}"
        
    - name: Fail if not all services are running
      fail:
        msg: "Not all expected LME services are running. Only {{ containers_status.stdout_lines | select('match', '^lme') | list | length }} of 5 expected services were detected."
      when: containers_status.rc != 0
      
    - name: Wait for fleet server to complete enrollment
      pause:
        seconds: 30
        prompt: "Waiting for fleet server enrollment to complete..."
        
    - name: Set FLEET_ENROLL=0 to prevent re-enrollment on restart
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^FLEET_ENROLL="
        line: "FLEET_ENROLL=0"
        insertafter: EOF
        state: present
      register: fleet_enroll_disabled
      
    - name: Set FLEET_SERVER_ENABLE=0 to prevent re-bootstrap on restart
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^FLEET_SERVER_ENABLE="
        line: "FLEET_SERVER_ENABLE=0"
        insertafter: EOF
        state: present
      register: fleet_server_enable_disabled
      
    - name: Set KIBANA_FLEET_SETUP=0 to prevent re-setup on restart
      lineinfile:
        path: "{{ env_file_path }}"
        regexp: "^KIBANA_FLEET_SETUP="
        line: "KIBANA_FLEET_SETUP=0"
        insertafter: EOF
        state: present
      register: kibana_fleet_setup_disabled
      
    - name: Update LME version in environment file
      lineinfile:
        path: "{{ env_file_path }}"
        line: "LME_VERSION={{ new_lme_version }}"
        regexp: "^LME_VERSION="
        state: present
      register: version_updated
      
    - name: Update LME version file
      copy:
        content: "{{ new_lme_version }}"
        dest: "/etc/lme/version"
      
    - name: Upgrade complete message
      debug:
        msg: |
          {% set message_lines = [
            'LME has been successfully upgraded to version ' + new_lme_version,
            '',
            'Container versions:',
            'Elasticsearch: ' + elasticsearch_image,
            'Elastic Agent: docker.elastic.co/beats/elastic-agent:' + elasticsearch_version,
            'Kibana: ' + kibana_image,
            'Wazuh: ' + wazuh_image,
            'ElastAlert: ' + elastalert_image,
            '',
            'Configuration updates:',
            '- Quadlet files: Updated with latest container configurations',
            '- Previous quadlet files backed up to: ' + quadlet_backup_dir.path,
            '- Systemd daemon: Reloaded to recognize changes'
          ] %}
          {{ dict(range(1, message_lines|length + 1) | zip(message_lines)) }} 