---
# Debian-specific Nix setup

- name: Update apt cache
  apt:
    update_cache: yes
  become: yes

- name: Install nix packages
  apt:
    name:
      - nix-bin
      - nix-setup-systemd
    state: present
  become: yes

- name: Add Nix channel
  command: nix-channel --add https://nixos.org/channels/nixpkgs-unstable nixpkgs
  become: yes

- name: Update Nix channel
  command: nix-channel --update
  become: yes

- name: Add user to nix-users group
  user:
    name: "{{ install_user }}"
    groups: nix-users
    append: yes
  become: yes

- name: Install required packages
  command: nix-env -iA nixpkgs.podman nixpkgs.docker-compose
  become: yes 