---
# Debian-specific tasks for base role

- name: Update apt cache
  apt:
    update_cache: yes
  become: yes
  when: not (offline_mode | default(false))

- name: Install required Debian packages
  apt:
    name: "{{ debian_packages }}"
    state: present
  become: yes
  when: not (offline_mode | default(false))

- name: Set timezone information
  debconf:
    name: tzdata
    question: tzdata/Areas
    value: "{{ timezone_area | default('Etc') }}"
    vtype: select
  become: yes

- name: Set timezone city
  debconf:
    name: tzdata
    question: tzdata/Zones/{{ timezone_area | default('Etc') }}
    value: "{{ timezone_zone | default('UTC') }}"
    vtype: select
  become: yes 