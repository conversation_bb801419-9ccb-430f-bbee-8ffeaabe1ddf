---
# Password setup tasks

- name: Create Vault password
  shell: |
    password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30)
    while ! echo -n $password | grep -Eq '[$@!]+';do password=$(</dev/urandom tr -dc A-Za-z0-9$@! | head -c30);done
    echo -n $password
  args:
    executable: /bin/bash
  register: vault_password

- name: Set ANSIBLE_VAULT_PASSWORD
  set_fact:
    ansible_vault_password: "{{ vault_password.stdout }}"

#TODO: make this a bit nicer
- name: Check password complexity
  block:
    - name: Check password length
      assert:
        that:
          - ansible_vault_password | length >= min_length
        fail_msg: "Input is too short. It should be at least {{ min_length }} characters long."

    - name: Generate SHA-1 hash of the password
      shell: "echo -n '{{ ansible_vault_password }}' | openssl sha1 | awk '{print $2}'"
      args: 
        executable: /bin/bash
      register: password_hash

    - name: Set prefix and suffix
      set_fact:
        prefix: "{{ password_hash.stdout[0:5] }}"
        suffix: "{{ password_hash.stdout[5:] }}"

    - name: Check against HIBP API
      uri:
        url: "https://api.pwnedpasswords.com/range/{{ prefix }}"
        method: GET
        return_content: yes
      register: hibp_response

    - name: Fail if password is found in breaches
      fail:
        msg: "The password has been found in breaches... this should only happen if you provided a password via the cli... choose a different password"
      when: hibp_response.content | regex_search(suffix)

- name: check if vault-pass.sh is created
  stat:
    path: "{{ password_file }}"
  register: pass_file
  become: yes

- name: Create vault-pass.sh with secure permissions (only if it doesn't exist!)
  copy:
    dest: "{{ password_file }}"
    content: |
      #!/bin/bash
      echo "{{ ansible_vault_password }}"
    mode: '0700'
  when: not pass_file.stat.exists
  become: yes

- name: Ensure ANSIBLE_VAULT_PASSWORD_FILE is set in .profile
  lineinfile:
    path: /root/.profile
    line: "export ANSIBLE_VAULT_PASSWORD_FILE=\"{{ password_file }}\""
    state: present
  become: yes

- name: Ensure ANSIBLE_VAULT_PASSWORD_FILE is set in .bashrc
  lineinfile:
    path: /root/.bashrc
    line: "export ANSIBLE_VAULT_PASSWORD_FILE=\"{{ password_file }}\""
    state: present
  become: yes

- name: Setup Podman secrets configuration
  copy:
    dest: "{{ user_secrets_conf }}"
    content: |
      [secrets]
      driver = "shell"

      [secrets.opts]
      list = "ls {{ user_vault_dir }}"
      lookup = "ansible-vault view {{ user_vault_dir }}/$SECRET_ID | tr -d '\n'"
      store = "cat > {{ user_vault_dir }}/$SECRET_ID && chmod 700 {{ user_vault_dir }}/$SECRET_ID && ansible-vault encrypt {{ user_vault_dir }}/$SECRET_ID"
      delete = "rm {{ user_vault_dir }}/$SECRET_ID"
    mode: '0600'
  become: yes

- name: setup root overlay-fs usage
  copy:
    dest: "{{ user_storage_conf }}"
    content: |
      [storage]
      driver = "overlay"

      [storage.options.overlay]
      mount_program = "/usr/bin/fuse-overlayfs"

    mode: '0600'
  become: yes

- name: Create /etc/containers
  file:
    path: /etc/containers/
    state: directory
    owner: "root"
    group: "root"
    mode: '0744'
  become: yes

- name: setup global overlay-fs usage
  copy:
    dest: "{{ global_storage_conf }}"
    content: |
      [storage]
      driver = "overlay"
      runroot = "{{ storage_runroot | default('/run/containers/storage') }}"
      graphroot = "{{ storage_graphroot | default('/var/lib/containers/storage') }}"
      [storage.options.overlay]
      mount_program = "/usr/bin/fuse-overlayfs"

    mode: '0600'
  become: yes 