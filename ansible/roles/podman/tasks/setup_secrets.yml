---
# Extract and set global secrets

- name: Source extract_secrets and capture output
  ansible.builtin.shell: |
    set -a
    source {{ playbook_dir }}/../scripts/extract_secrets.sh -q
    echo "elastic=$elastic"
    echo "wazuh=$wazuh"
    echo "kibana_system=$kibana_system"
    echo "wazuh_api=$wazuh_api"
  args:
    executable: /bin/bash
  register: extract_secrets_vars
  no_log: "{{ not debug_mode }}"
  become: yes

- name: Debug extract_secrets output
  debug:
    var: extract_secrets_vars.stdout_lines
  when: debug_mode | bool

- name: Set global secret variables
  ansible.builtin.set_fact:
    global_secrets: "{{ global_secrets | default({}) | combine({ item.split('=', 1)[0]: item.split('=', 1)[1] }) }}"
  loop: "{{ extract_secrets_vars.stdout_lines }}"
  when: item != '' and '=' in item
  no_log: "{{ not debug_mode }}"
  delegate_to: localhost

- name: Verify global secrets were set
  debug:
    msg: "Global secrets keys: {{ global_secrets.keys() | list }}"
  when: debug_mode | bool

- name: Fail if required secrets are missing
  fail:
    msg: "Required secrets are missing. Found: {{ global_secrets.keys() | list }}"
  when: 
    - not global_secrets.elastic is defined
    - not global_secrets.wazuh is defined
    - not global_secrets.kibana_system is defined
    - not global_secrets.wazuh_api is defined 