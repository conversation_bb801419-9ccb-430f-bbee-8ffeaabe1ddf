---
- name: Setup LME
  hosts: localhost
  connection: local
  become: no  # Default to no privilege escalation
  vars:
    # Common variables
    clone_directory: "{{ clone_dir | default(playbook_dir + '/..') }}"
    install_user: "{{ ansible_user_id }}"
    debug_mode: false  # Default debug mode to false
    offline_mode: false  # Default offline mode to false - set to true for offline installation
    
    # Storage configuration
    storage_runroot: "/run/containers/storage"
    storage_graphroot: "/var/lib/containers/storage"
    
    # Directory configuration
    config_dir: "/etc/lme"
    user_config_dir: "/root/.config/containers"
    user_vault_dir: "/etc/lme/vault"
    user_secrets_conf: "/root/.config/containers/containers.conf"
    user_storage_conf: "/root/.config/containers/storage.conf"
    global_storage_conf: "/etc/containers/storage.conf"
    password_file: "/etc/lme/pass.sh"
    
    # Password configuration
    min_length: 12
    
    # Elasticsearch configuration
    es_port: 9200
    kibana_port: 5601
    fleet_port: 8220
    
    # Kibana version
    kibana_version: "8.18.0"
    
    # Default timezone settings
    timezone_area: "Etc"  # Change to your area: America, Europe, Asia, etc.
    timezone_zone: "UTC"  # Change to your timezone: New_York, London, Tokyo, etc.
  roles:
    - role: base
      tags: ['base', 'all']
    - role: nix
      tags: ['base', 'all']
    - role: podman
      tags: ['system', 'all']
    - role: elasticsearch
      tags: ['system', 'all']
    - role: kibana
      tags: ['system', 'all']
    - role: dashboards
      tags: ['system', 'all']
    - role: wazuh
      tags: ['system', 'all']
    - role: fleet
      tags: ['system', 'all']
    